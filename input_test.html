<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>输入框测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 0 auto;
        }
        
        .operation-content {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .operation-content input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.2s ease;
            outline: none;
        }
        
        .operation-content input:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }
        
        .operation-content input:hover {
            border-color: #adb5bd;
        }
        
        .operation-content button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .operation-content button:hover {
            background-color: #2980b9;
        }
        
        .status {
            padding: 10px;
            margin-top: 10px;
            border-radius: 4px;
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>输入框焦点测试</h2>
        <p>测试存取款输入框是否能正常获得焦点和输入</p>
        
        <div class="operation-content">
            <input type="number" id="deposit-amount" placeholder="输入存款金额" min="1">
            <button id="deposit-btn">存入</button>
        </div>
        
        <div class="operation-content">
            <input type="number" id="withdraw-amount" placeholder="输入取款金额" min="1">
            <button id="withdraw-btn">取出</button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        // 模拟修复后的事件处理
        document.addEventListener('DOMContentLoaded', function() {
            const depositInput = document.getElementById('deposit-amount');
            const withdrawInput = document.getElementById('withdraw-amount');
            const depositBtn = document.getElementById('deposit-btn');
            const withdrawBtn = document.getElementById('withdraw-btn');
            const status = document.getElementById('status');
            
            function showStatus(message) {
                status.textContent = message;
                status.style.display = 'block';
                setTimeout(() => {
                    status.style.display = 'none';
                }, 3000);
            }
            
            // 防止输入框失去焦点
            [depositInput, withdrawInput].forEach(input => {
                if (input) {
                    input.addEventListener('mousedown', function(e) {
                        e.stopPropagation();
                    });
                    
                    input.addEventListener('click', function(e) {
                        e.stopPropagation();
                        this.focus();
                        showStatus('输入框已获得焦点');
                    });
                    
                    input.addEventListener('focus', function(e) {
                        e.stopPropagation();
                        showStatus('输入框获得焦点');
                    });
                    
                    input.addEventListener('blur', function(e) {
                        showStatus('输入框失去焦点');
                    });
                    
                    // 添加回车键支持
                    input.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            const amount = parseInt(this.value);
                            if (amount > 0) {
                                if (this.id === 'deposit-amount') {
                                    showStatus('存款: ¥' + amount);
                                } else if (this.id === 'withdraw-amount') {
                                    showStatus('取款: ¥' + amount);
                                }
                                this.value = '';
                            }
                        }
                    });
                }
            });
            
            // 按钮事件
            depositBtn.addEventListener('click', function() {
                const amount = parseInt(depositInput.value);
                if (amount > 0) {
                    showStatus('存款: ¥' + amount);
                    depositInput.value = '';
                } else {
                    showStatus('请输入有效的存款金额');
                }
            });
            
            withdrawBtn.addEventListener('click', function() {
                const amount = parseInt(withdrawInput.value);
                if (amount > 0) {
                    showStatus('取款: ¥' + amount);
                    withdrawInput.value = '';
                } else {
                    showStatus('请输入有效的取款金额');
                }
            });
            
            // 模拟可能干扰的全局事件监听器（已修复）
            document.addEventListener('click', function(e) {
                // 只处理按钮点击，避免干扰输入框
                if (e.target.tagName !== 'BUTTON' && !e.target.closest('button')) {
                    return;
                }
                console.log('全局点击事件（仅处理按钮）:', e.target);
            });
        });
    </script>
</body>
</html>
