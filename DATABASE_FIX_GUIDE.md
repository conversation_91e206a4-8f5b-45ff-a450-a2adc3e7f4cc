# 彩票系统数据库修复指南

## 问题描述
您遇到的错误信息：
```
Error: LotteryMaster_Tu<PERSON><PERSON> was unable to execute a query!
Query: DROP PROCEDURE IF EXISTS CleanupOldLotteryData;
Cannot load from mysql.proc. The table is probably corrupted
```

这个错误通常由以下原因引起：
1. MySQL版本兼容性问题（MySQL 8.0+不再使用mysql.proc表）
2. mysql.proc表损坏
3. 数据库权限不足

## 解决方案

### 方案1：使用修复后的代码（推荐）
我已经修改了 `server/database.lua` 文件，使其兼容不同版本的MySQL：

1. **自动检测MySQL版本**
2. **兼容MySQL 8.0+**
3. **提供手动清理模式作为备选方案**

### 方案2：手动数据库修复

#### 步骤1：检查MySQL版本
```sql
SELECT VERSION();
```

#### 步骤2：如果是MySQL 8.0+
MySQL 8.0+已经不使用mysql.proc表，这是正常的。代码已经适配。

#### 步骤3：如果是MySQL 5.x且mysql.proc表损坏
```sql
-- 修复mysql.proc表
REPAIR TABLE mysql.proc;

-- 或者重建mysql.proc表
DROP TABLE IF EXISTS mysql.proc;
-- 然后重启MySQL服务
```

#### 步骤4：检查权限
确保数据库用户有以下权限：
```sql
GRANT CREATE ROUTINE, ALTER ROUTINE, EXECUTE ON *.* TO 'your_user'@'%';
GRANT EVENT ON *.* TO 'your_user'@'%';
FLUSH PRIVILEGES;
```

### 方案3：使用修复脚本

1. **将 `database_repair.lua` 文件放入服务器目录**
2. **在服务器控制台执行：**
   ```
   /lottery_repair
   ```

### 方案4：禁用存储过程（临时解决方案）

如果以上方案都不行，可以临时禁用存储过程功能：

在 `config.lua` 中添加：
```lua
Config.Database = {
    updateInterval = 60000,
    useStoredProcedures = false  -- 禁用存储过程
}
```

## 验证修复

修复后，检查以下内容：

1. **服务器日志不再显示错误**
2. **彩票系统正常运行**
3. **数据库连接正常**

## 预防措施

1. **定期备份数据库**
2. **使用稳定版本的MySQL**
3. **确保数据库用户权限充足**
4. **定期检查数据库健康状态**

## 常见问题

### Q: 修复后仍然有错误？
A: 检查数据库连接配置和用户权限

### Q: 数据会丢失吗？
A: 不会，修复只是重建存储过程，不会影响数据

### Q: 需要重启服务器吗？
A: 建议重启以确保所有更改生效

## 联系支持

如果问题仍然存在，请提供：
1. MySQL版本信息
2. 完整的错误日志
3. 数据库配置信息（隐藏敏感信息）

---

**注意：在进行任何数据库操作前，请务必备份数据库！**
