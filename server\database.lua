-- 数据库管理模块

-- 获取框架对象
local ESX, QBCore = nil, nil
if Config.Framework == 'ESX' then
    ESX = exports['es_extended']:getSharedObject()
elseif Config.Framework == 'QB' then
    QBCore = exports['qb-core']:GetCoreObject()
end

-- 调试打印函数 - 只在调试模式开启时打印
local function DebugPrint(message, level)
    if Config.Debug and Config.Debug.enabled then
        level = level or "^7" -- 默认白色
        print(level .. "[彩票系统-调试] " .. message)
    end
end

-- 系统消息打印函数 - 总是打印重要的系统消息
local function SystemPrint(message, level)
    level = level or "^7" -- 默认白色
    print(level .. "[彩票系统] " .. message)
end

-- 初始化数据库表 (优化版本)
function InitializeDatabase()
    SystemPrint('^3[彩票系统] ^7正在初始化数据库和性能优化...')

    -- 设置数据库连接优化参数
    MySQL.Async.execute('SET SESSION wait_timeout = 300', {})
    MySQL.Async.execute('SET SESSION interactive_timeout = 300', {})
    MySQL.Async.execute('SET SESSION TRANSACTION ISOLATION LEVEL READ COMMITTED', {})
    -- 移除查询缓存设置，因为现代MySQL版本已不支持

    -- 彩票店表
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `lottery_shops` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `coords` text NOT NULL,
            `heading` float DEFAULT 0.0,
            `is_active` tinyint(1) DEFAULT 1,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_is_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
    ]], {})

    -- 彩票记录表 (优化索引)
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `lottery_tickets` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `player_id` varchar(50) NOT NULL,
            `player_name` varchar(100) NOT NULL,
            `lottery_type` enum('double_ball','super_lotto','arrange_five') NOT NULL,
            `numbers` text NOT NULL,
            `red_balls` text DEFAULT NULL,
            `blue_ball` int(11) DEFAULT NULL,
            `front_balls` text DEFAULT NULL,
            `back_balls` text DEFAULT NULL,
            `arrange_number` varchar(5) DEFAULT NULL,
            `purchase_time` DATETIME NOT NULL,
            `draw_date` DATETIME NOT NULL,
            `draw_period` varchar(20) DEFAULT NULL,
            `is_winning` tinyint(1) DEFAULT 0,
            `prize_level` int(11) DEFAULT NULL,
            `prize_amount` int(11) DEFAULT 0,
            `reward_data` text DEFAULT NULL,
            `is_claimed` tinyint(1) DEFAULT 0,
            `claimed_time` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `idx_player_lottery_type` (`player_id`, `lottery_type`),
            KEY `idx_draw_date_type` (`draw_date`, `lottery_type`),
            KEY `idx_purchase_time` (`purchase_time`),
            KEY `idx_is_winning_claimed` (`is_winning`, `is_claimed`),
            KEY `idx_draw_period_type` (`draw_period`, `lottery_type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
    ]], {})
    
    -- 刮刮乐记录表 (优化索引)
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `scratch_cards` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `player_id` varchar(50) NOT NULL,
            `player_name` varchar(100) NOT NULL,
            `card_type` varchar(50) NOT NULL,
            `card_name` varchar(100) NOT NULL,
            `purchase_price` int(11) NOT NULL,
            `ticket_data` text NOT NULL,
            `is_scratched` tinyint(1) DEFAULT 0,
            `prize_amount` int(11) DEFAULT 0,
            `is_claimed` tinyint(1) DEFAULT 0,
            `purchase_time` timestamp DEFAULT CURRENT_TIMESTAMP,
            `scratch_time` timestamp NULL DEFAULT NULL,
            `claimed_time` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `idx_player_card_type` (`player_id`, `card_type`),
            KEY `idx_is_scratched_claimed` (`is_scratched`, `is_claimed`),
            KEY `idx_purchase_time` (`purchase_time`),
            KEY `idx_prize_amount` (`prize_amount`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
    ]], {})
    
    -- 奖池表 (优化索引)
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `prize_pools` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `lottery_type` enum('double_ball','super_lotto','arrange_five') NOT NULL,
            `current_amount` bigint(20) DEFAULT 0,
            `total_contribution` bigint(20) DEFAULT 0,
            `last_draw_amount` bigint(20) DEFAULT 0,
            `rollover_count` int(11) DEFAULT 0,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `lottery_type` (`lottery_type`),
            KEY `idx_lottery_type_updated` (`lottery_type`, `updated_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
    ]], {})
    
    -- 开奖历史表 (优化索引)
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `draw_history` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `lottery_type` enum('double_ball','super_lotto','arrange_five') NOT NULL,
            `period_number` varchar(20) NOT NULL,
            `draw_date` DATETIME NOT NULL,
            `winning_numbers` text NOT NULL,
            `red_balls` text DEFAULT NULL,
            `blue_ball` int(11) DEFAULT NULL,
            `front_balls` text DEFAULT NULL,
            `back_balls` text DEFAULT NULL,
            `arrange_number` varchar(5) DEFAULT NULL,
            `total_sales` bigint(20) DEFAULT 0,
            `total_winners` int(11) DEFAULT 0,
            `prize_details` text DEFAULT NULL,
            `jackpot_amount` bigint(20) DEFAULT 0,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `lottery_period` (`lottery_type`, `period_number`),
            KEY `idx_lottery_type_date` (`lottery_type`, `draw_date`),
            KEY `idx_period_type` (`period_number`, `lottery_type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
    ]], {})
    
    -- 中奖公告表
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `lottery_announcements` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `player_name` varchar(100) NOT NULL,
            `lottery_type` varchar(50) NOT NULL,
            `prize_amount` bigint(20) NOT NULL,
            `announcement_text` text NOT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_lottery_type_time` (`lottery_type`, `created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
    ]], {})

    -- 彩票店账户表
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `lottery_shop_accounts` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `balance` bigint(20) DEFAULT 0,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_updated_at` (`updated_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
    ]], {})

    -- 交易记录表
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `lottery_transactions` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `transaction_type` varchar(50) NOT NULL,
            `amount` bigint(20) NOT NULL,
            `description` text NOT NULL,
            `player_id` varchar(50) DEFAULT NULL,
            `player_name` varchar(100) DEFAULT NULL,
            `admin_name` varchar(100) DEFAULT NULL,
            `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_transaction_type_time` (`transaction_type`, `timestamp`),
            KEY `idx_player_time` (`player_id`, `timestamp`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
    ]], {})

    -- 员工表
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `lottery_employees` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `employee_id` varchar(50) NOT NULL,
            `name` varchar(100) NOT NULL,
            `level` int(11) DEFAULT 1,
            `salary` int(11) DEFAULT 0,
            `bonus` int(11) DEFAULT 0,
            `status` varchar(20) DEFAULT 'active',
            `hire_date` timestamp DEFAULT CURRENT_TIMESTAMP,
            `last_paid` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `employee_id` (`employee_id`),
            KEY `idx_employee_status` (`employee_id`, `status`),
            KEY `idx_hire_date` (`hire_date`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
    ]], {})

    -- 管理日志表
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `lottery_admin_logs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `admin_id` varchar(50) NOT NULL,
            `admin_name` varchar(100) NOT NULL,
            `action` varchar(100) NOT NULL,
            `details` text NOT NULL,
            `amount` int(11) DEFAULT 0,
            `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_admin_action_time` (`admin_id`, `action`, `timestamp`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
    ]], {})

    -- 离线奖品表
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `lottery_offline_prizes` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `player_id` varchar(50) NOT NULL,
            `player_name` varchar(50) NOT NULL,
            `prize_amount` int(11) NOT NULL,
            `prize_type` varchar(50) NOT NULL,
            `ticket_id` int(11) NOT NULL,
            `timestamp` timestamp DEFAULT CURRENT_TIMESTAMP,
            `claimed` tinyint(1) DEFAULT 0,
            PRIMARY KEY (`id`),
            KEY `idx_player_claimed` (`player_id`, `claimed`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
    ]], {})

    -- 检查并添加新字段
    MySQL.Async.fetchAll("SHOW COLUMNS FROM lottery_tickets LIKE 'reward_data'", {}, function(result)
        if not result or #result == 0 then
            -- reward_data字段不存在，添加它
            MySQL.Async.execute("ALTER TABLE lottery_tickets ADD COLUMN reward_data TEXT DEFAULT NULL AFTER prize_amount", {}, function(success)
                if success then
                    SystemPrint('^2[彩票系统] ^7已添加reward_data字段到lottery_tickets表')
                else
                    SystemPrint('^1[彩票系统] ^7添加reward_data字段失败')
                end
            end)
        else
            SystemPrint('^3[彩票系统] ^7reward_data字段已存在')
        end
    end)

    -- 延迟执行优化任务
    SetTimeout(2000, function()
        OptimizeDatabasePerformance()
    end)

    SystemPrint('^2[彩票系统] ^7数据库表初始化完成')
end

-- 数据库性能优化函数
function OptimizeDatabasePerformance()
    SystemPrint('^3[彩票系统] ^7正在执行数据库性能优化...')

    -- 检查MySQL版本以确定是否支持存储过程
    MySQL.Async.fetchScalar('SELECT VERSION()', {}, function(version)
        if not version then
            SystemPrint('^1[彩票系统] ^7无法获取MySQL版本信息，跳过存储过程创建')
            return
        end

        SystemPrint('^3[彩票系统] ^7MySQL版本: ' .. tostring(version))

        -- 检查是否为MySQL 8.0+
        local majorVersion = tonumber(string.match(version, "^(%d+)"))
        if majorVersion and majorVersion >= 8 then
            SystemPrint('^3[彩票系统] ^7检测到MySQL 8.0+，使用兼容模式')
            -- 对于MySQL 8.0+，直接创建存储过程而不删除
            CreateCleanupProcedure()
        else
            SystemPrint('^3[彩票系统] ^7检测到MySQL 5.x，使用传统模式')
            -- 对于MySQL 5.x，先删除再创建
            MySQL.Async.execute([[
                DROP PROCEDURE IF EXISTS CleanupOldLotteryData;
            ]], {}, function(success)
                if success then
                    SystemPrint('^3[彩票系统] ^7已删除旧的清理存储过程')
                else
                    SystemPrint('^3[彩票系统] ^7删除存储过程失败或不存在，继续创建新的')
                end
                CreateCleanupProcedure()
            end)
        end
    end)
end

-- 创建清理存储过程的函数
function CreateCleanupProcedure()
    MySQL.Async.execute([[
        CREATE PROCEDURE IF NOT EXISTS CleanupOldLotteryData()
        BEGIN
            DECLARE EXIT HANDLER FOR SQLEXCEPTION
            BEGIN
                ROLLBACK;
                RESIGNAL;
            END;

            START TRANSACTION;

            -- 删除30天前的已兑奖彩票记录
            DELETE FROM lottery_tickets
            WHERE is_claimed = 1
            AND claimed_time < DATE_SUB(NOW(), INTERVAL 30 DAY)
            LIMIT 1000;

            -- 删除30天前的已刮开且已兑奖的刮刮乐记录
            DELETE FROM scratch_cards
            WHERE is_scratched = 1
            AND is_claimed = 1
            AND claimed_time < DATE_SUB(NOW(), INTERVAL 30 DAY)
            LIMIT 1000;

            -- 删除90天前的管理日志
            DELETE FROM lottery_admin_logs
            WHERE timestamp < DATE_SUB(NOW(), INTERVAL 90 DAY)
            LIMIT 1000;

            -- 删除90天前的交易记录
            DELETE FROM lottery_transactions
            WHERE timestamp < DATE_SUB(NOW(), INTERVAL 90 DAY)
            LIMIT 1000;

            -- 删除7天前的中奖公告
            DELETE FROM lottery_announcements
            WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY)
            LIMIT 1000;

            COMMIT;
        END
    ]], {}, function(success)
        if success then
            SystemPrint('^2[彩票系统] ^7清理存储过程创建成功')
            CreateCleanupEvent()
        else
            SystemPrint('^1[彩票系统] ^7清理存储过程创建失败，将使用手动清理模式')
            -- 如果存储过程创建失败，启用手动清理模式
            StartManualCleanup()
        end
    end)
end

-- 创建清理事件的函数
function CreateCleanupEvent()
    -- 先尝试删除现有事件
    MySQL.Async.execute([[
        DROP EVENT IF EXISTS cleanup_lottery_data;
    ]], {}, function()
        -- 创建新的清理事件
        MySQL.Async.execute([[
            CREATE EVENT IF NOT EXISTS cleanup_lottery_data
            ON SCHEDULE EVERY 1 DAY
            STARTS CURRENT_TIMESTAMP
            DO
            CALL CleanupOldLotteryData();
        ]], {}, function(eventSuccess)
            if eventSuccess then
                SystemPrint('^2[彩票系统] ^7定期清理事件创建成功')

                -- 启用事件调度器
                MySQL.Async.execute('SET GLOBAL event_scheduler = ON', {}, function(schedulerSuccess)
                    if schedulerSuccess then
                        SystemPrint('^2[彩票系统] ^7事件调度器已启用')
                    else
                        SystemPrint('^3[彩票系统] ^7事件调度器启用失败（可能需要管理员权限），将使用手动清理模式')
                        StartManualCleanup()
                    end
                end)
            else
                SystemPrint('^1[彩票系统] ^7定期清理事件创建失败，将使用手动清理模式')
                StartManualCleanup()
            end
        end)
    end)
end

-- 手动清理模式（当存储过程或事件创建失败时使用）
function StartManualCleanup()
    SystemPrint('^3[彩票系统] ^7启用手动清理模式，将每24小时执行一次数据清理')

    -- 创建定时器，每24小时执行一次清理
    SetTimeout(24 * 60 * 60 * 1000, function() -- 24小时
        ManualCleanupData()
        StartManualCleanup() -- 递归调用以保持定时清理
    end)
end

-- 手动清理数据函数
function ManualCleanupData()
    SystemPrint('^3[彩票系统] ^7开始手动清理过期数据...')

    -- 删除30天前的已兑奖彩票记录
    MySQL.Async.execute([[
        DELETE FROM lottery_tickets
        WHERE is_claimed = 1
        AND claimed_time < DATE_SUB(NOW(), INTERVAL 30 DAY)
        LIMIT 1000
    ]], {}, function(result)
        if result then
            SystemPrint('^2[彩票系统] ^7已清理过期彩票记录')
        end
    end)

    -- 删除30天前的已刮开且已兑奖的刮刮乐记录
    MySQL.Async.execute([[
        DELETE FROM scratch_cards
        WHERE is_scratched = 1
        AND is_claimed = 1
        AND claimed_time < DATE_SUB(NOW(), INTERVAL 30 DAY)
        LIMIT 1000
    ]], {}, function(result)
        if result then
            SystemPrint('^2[彩票系统] ^7已清理过期刮刮乐记录')
        end
    end)

    -- 删除90天前的管理日志
    MySQL.Async.execute([[
        DELETE FROM lottery_admin_logs
        WHERE timestamp < DATE_SUB(NOW(), INTERVAL 90 DAY)
        LIMIT 1000
    ]], {}, function(result)
        if result then
            SystemPrint('^2[彩票系统] ^7已清理过期管理日志')
        end
    end)

    -- 删除90天前的交易记录
    MySQL.Async.execute([[
        DELETE FROM lottery_transactions
        WHERE timestamp < DATE_SUB(NOW(), INTERVAL 90 DAY)
        LIMIT 1000
    ]], {}, function(result)
        if result then
            SystemPrint('^2[彩票系统] ^7已清理过期交易记录')
        end
    end)

    -- 删除7天前的中奖公告
    MySQL.Async.execute([[
        DELETE FROM lottery_announcements
        WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY)
        LIMIT 1000
    ]], {}, function(result)
        if result then
            SystemPrint('^2[彩票系统] ^7已清理过期中奖公告')
        end
    end)

    SystemPrint('^2[彩票系统] ^7手动数据清理完成')
end

    -- 初始化彩票店账户
    MySQL.Async.execute([[
        INSERT IGNORE INTO lottery_shop_accounts (id, balance) VALUES (1, 0);
    ]], {})

    -- 初始化奖池
    MySQL.Async.execute([[
        INSERT IGNORE INTO prize_pools (lottery_type, current_amount) VALUES
        ('double_ball', 5000000),
        ('super_lotto', 8000000),
        ('arrange_five', 1000000);
    ]], {})

    DebugPrint('^2数据库性能优化完成')
end

-- 数据库健康检查
function DatabaseHealthCheck()
    MySQL.Async.fetchScalar('SELECT 1', {}, function(result)
        if result then
            DebugPrint("^2[彩票系统] ^7数据库连接正常")
        else
            SystemPrint("^1[彩票系统] ^7数据库连接异常！")
        end
    end)
end

-- 数据库统计监控
local DatabaseStats = {
    queries = 0,
    errors = 0,
    slowQueries = 0,
    lastReset = GetGameTimer(),
    connectionIssues = 0
}

-- 记录数据库查询统计
function RecordDatabaseQuery(queryTime, isError)
    DatabaseStats.queries = DatabaseStats.queries + 1

    if isError then
        DatabaseStats.errors = DatabaseStats.errors + 1
    end

    if queryTime and queryTime > 150 then -- 超过150ms的查询
        DatabaseStats.slowQueries = DatabaseStats.slowQueries + 1
        DebugPrint(string.format("^3慢查询检测: 耗时 %dms", queryTime))
    end

    -- 每小时重置统计
    local currentTime = GetGameTimer()
    if currentTime - DatabaseStats.lastReset > 3600000 then
        DebugPrint(string.format("^2数据库统计 - 查询: %d, 错误: %d, 慢查询: %d, 连接问题: %d",
            DatabaseStats.queries, DatabaseStats.errors, DatabaseStats.slowQueries, DatabaseStats.connectionIssues))

        DatabaseStats = {
            queries = 0,
            errors = 0,
            slowQueries = 0,
            lastReset = currentTime,
            connectionIssues = 0
        }
    end
end

-- 定期健康检查线程
CreateThread(function()
    Wait(60000) -- 启动后1分钟开始检查
    while true do
        Wait(300000) -- 每5分钟检查一次
        DatabaseHealthCheck()
    end
end)

-- 定期清理缓存线程
CreateThread(function()
    while true do
        Wait(1800000) -- 每30分钟清理一次过期缓存
        if Cache and Cache.ClearExpired then
            Cache:ClearExpired()
            DebugPrint("^3[彩票系统] ^7已清理过期缓存")
        end
    end
end)

-- 将函数设置为全局函数，确保其他模块可以访问
_G.InitializeDatabase = InitializeDatabase
_G.OptimizeDatabasePerformance = OptimizeDatabasePerformance
_G.DatabaseHealthCheck = DatabaseHealthCheck
_G.RecordDatabaseQuery = RecordDatabaseQuery
_G.CreateCleanupProcedure = CreateCleanupProcedure
_G.CreateCleanupEvent = CreateCleanupEvent
_G.StartManualCleanup = StartManualCleanup
_G.ManualCleanupData = ManualCleanupData

-- 导出函数供其他资源使用
exports('InitializeDatabase', InitializeDatabase)
exports('OptimizeDatabasePerformance', OptimizeDatabasePerformance)
exports('DatabaseHealthCheck', DatabaseHealthCheck)
exports('RecordDatabaseQuery', RecordDatabaseQuery)
exports('CreateCleanupProcedure', CreateCleanupProcedure)
exports('CreateCleanupEvent', CreateCleanupEvent)
exports('StartManualCleanup', StartManualCleanup)
exports('ManualCleanupData', ManualCleanupData)
exports('GetDatabaseStats', function()
    return DatabaseStats
end)

-- 重置数据库表结构
function ResetDatabaseTables()
    SystemPrint('^3[彩票系统] ^7正在重置数据库表结构...')
    
    -- 删除现有表
    MySQL.Async.execute('DROP TABLE IF EXISTS lottery_tickets', {})
    MySQL.Async.execute('DROP TABLE IF EXISTS draw_history', {})
    
    -- 重新创建表
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `lottery_tickets` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `player_id` varchar(50) NOT NULL,
            `player_name` varchar(100) NOT NULL,
            `lottery_type` enum('double_ball','super_lotto','arrange_five') NOT NULL,
            `numbers` text NOT NULL,
            `red_balls` text DEFAULT NULL,
            `blue_ball` int(11) DEFAULT NULL,
            `front_balls` text DEFAULT NULL,
            `back_balls` text DEFAULT NULL,
            `arrange_number` varchar(5) DEFAULT NULL,
            `purchase_time` DATETIME NOT NULL,
            `draw_date` DATETIME NOT NULL,
            `draw_period` varchar(20) DEFAULT NULL,
            `is_winning` tinyint(1) DEFAULT 0,
            `prize_level` int(11) DEFAULT NULL,
            `prize_amount` int(11) DEFAULT 0,
            `is_claimed` tinyint(1) DEFAULT 0,
            `claimed_time` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `player_id` (`player_id`),
            KEY `lottery_type` (`lottery_type`),
            KEY `draw_date` (`draw_date`),
            KEY `draw_period` (`draw_period`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]], {})
    
    MySQL.Async.execute([[
        CREATE TABLE IF NOT EXISTS `draw_history` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `lottery_type` enum('double_ball','super_lotto','arrange_five') NOT NULL,
            `period_number` varchar(20) NOT NULL,
            `draw_date` DATETIME NOT NULL,
            `winning_numbers` text NOT NULL,
            `red_balls` text DEFAULT NULL,
            `blue_ball` int(11) DEFAULT NULL,
            `front_balls` text DEFAULT NULL,
            `back_balls` text DEFAULT NULL,
            `arrange_number` varchar(5) DEFAULT NULL,
            `total_sales` bigint(20) DEFAULT 0,
            `total_winners` int(11) DEFAULT 0,
            `prize_details` text DEFAULT NULL,
            `jackpot_amount` bigint(20) DEFAULT 0,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `lottery_period` (`lottery_type`, `period_number`),
            KEY `draw_date` (`draw_date`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ]], {})
    
    SystemPrint('^2[彩票系统] ^7数据库表结构重置完成')
end

-- 批量插入队列系统
local InsertQueue = {
    tickets = {}, -- 彩票记录队列
    lastInsertTime = 0, -- 上次批量插入时间
    insertInterval = Config.InsertQueue and Config.InsertQueue.interval or 10000, -- 批量插入间隔(毫秒)
    maxQueueSize = Config.InsertQueue and Config.InsertQueue.maxSize or 50, -- 队列最大长度，达到此长度会触发批量插入
    processing = false, -- 是否正在处理队列
    enabled = Config.InsertQueue and Config.InsertQueue.enabled or true, -- 是否启用批量插入队列
    debug = Config.InsertQueue and Config.InsertQueue.debug or false, -- 是否打印队列调试信息
    stats = {
        totalInserted = 0, -- 总共插入的记录数
        totalBatches = 0, -- 总共执行的批次数
        byType = {}, -- 按类型统计
        lastStats = 0 -- 上次统计时间
    }
}

-- 打印队列统计信息
function PrintQueueStats()
    if not InsertQueue.enabled then
        return
    end
    
    local currentTime = GetGameTimer()
    local timeSinceLastStats = currentTime - InsertQueue.stats.lastStats
    
    -- 每5分钟打印一次统计信息
    if timeSinceLastStats < 5 * 60 * 1000 and not InsertQueue.debug then
        return
    end
    
    InsertQueue.stats.lastStats = currentTime
    
    DebugPrint("^3[彩票系统队列统计] ^7==========================================")
    DebugPrint("^3[彩票系统队列统计] ^7总插入记录数: " .. InsertQueue.stats.totalInserted)
    DebugPrint("^3[彩票系统队列统计] ^7总执行批次数: " .. InsertQueue.stats.totalBatches)
    DebugPrint("^3[彩票系统队列统计] ^7当前队列长度: " .. #InsertQueue.tickets)

    -- 按类型统计
    for lotteryType, stats in pairs(InsertQueue.stats.byType) do
        DebugPrint("^3[彩票系统队列统计] ^7类型: " .. lotteryType)
        DebugPrint("^3[彩票系统队列统计] ^7  - 插入记录数: " .. stats.inserted)
        DebugPrint("^3[彩票系统队列统计] ^7  - 执行批次数: " .. stats.batches)
        DebugPrint("^3[彩票系统队列统计] ^7  - 平均每批记录: " .. (stats.batches > 0 and stats.inserted / stats.batches or 0))
    end

    DebugPrint("^3[彩票系统队列统计] ^7==========================================")
end

-- 更新队列统计信息
function UpdateQueueStats(lotteryType, insertedCount, batchCount)
    InsertQueue.stats.totalInserted = InsertQueue.stats.totalInserted + insertedCount
    InsertQueue.stats.totalBatches = InsertQueue.stats.totalBatches + batchCount
    
    if not InsertQueue.stats.byType[lotteryType] then
        InsertQueue.stats.byType[lotteryType] = {
            inserted = 0,
            batches = 0
        }
    end
    
    InsertQueue.stats.byType[lotteryType].inserted = InsertQueue.stats.byType[lotteryType].inserted + insertedCount
    InsertQueue.stats.byType[lotteryType].batches = InsertQueue.stats.byType[lotteryType].batches + batchCount
end

-- 定期打印队列统计信息
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(60000) -- 每分钟检查一次
        PrintQueueStats()
    end
end)

-- 添加彩票记录到队列
function AddToInsertQueue(params, callback)
    if not InsertQueue.enabled then
        -- 如果队列未启用，直接执行插入
        DirectInsertTicket(params, callback)
        return
    end
    
    -- 添加到队列
    table.insert(InsertQueue.tickets, {
        params = params,
        callback = callback,
        timestamp = GetGameTimer()
    })
    
    -- 检查队列大小是否达到阈值
    local queueSize = #InsertQueue.tickets
    local lotteryType = params[3] -- lottery_type参数位置
    local typeBatchSize = 0
    
    -- 获取该类型的批量大小
    if Config.InsertQueue and Config.InsertQueue.batchSize and Config.InsertQueue.batchSize[lotteryType] then
        typeBatchSize = Config.InsertQueue.batchSize[lotteryType]
    end
    
    -- 如果队列中该类型的记录数量达到批量大小，或总队列大小达到最大值，触发处理
    local typeCount = 0
    for _, item in ipairs(InsertQueue.tickets) do
        if item.params[3] == lotteryType then
            typeCount = typeCount + 1
        end
    end
    
    if (typeBatchSize > 0 and typeCount >= typeBatchSize) or queueSize >= InsertQueue.maxQueueSize then
        if InsertQueue.debug then
            SystemPrint("^3[彩票系统] ^7队列达到阈值，触发批量插入。类型: " .. lotteryType .. 
                ", 类型数量: " .. typeCount .. ", 总队列大小: " .. queueSize)
        end
        ProcessTicketQueue()
    end
end

-- 定期检查队列 (优化检查频率)
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(5000) -- 改为每5秒检查一次，减少CPU占用

        if InsertQueue.enabled and not InsertQueue.processing and #InsertQueue.tickets > 0 then
            local currentTime = GetGameTimer()
            local timeSinceLastInsert = currentTime - InsertQueue.lastInsertTime

            -- 如果达到间隔时间，处理队列
            if timeSinceLastInsert >= InsertQueue.insertInterval then
                if InsertQueue.debug then
                    SystemPrint("^3[彩票系统] ^7达到时间间隔，触发批量插入。队列大小: " .. #InsertQueue.tickets)
                end
                ProcessTicketQueue()
            end
        end
    end
end)

-- 直接插入彩票记录（不使用队列）
function DirectInsertTicket(params, callback)
    local playerId = params[1]
    local lotteryType = params[3]
    local query = ""
    
    if lotteryType == "double_ball" then
        query = "INSERT INTO lottery_tickets (player_id, player_name, lottery_type, numbers, draw_date, red_balls, blue_ball, draw_period, purchase_time) VALUES (?, ?, ?, ?, ?, ?, ?, NULL, ?)"
    elseif lotteryType == "super_lotto" then
        query = "INSERT INTO lottery_tickets (player_id, player_name, lottery_type, numbers, draw_date, front_balls, back_balls, draw_period, purchase_time) VALUES (?, ?, ?, ?, ?, ?, ?, NULL, ?)"
    elseif lotteryType == "arrange_five" then
        query = "INSERT INTO lottery_tickets (player_id, player_name, lottery_type, numbers, draw_date, arrange_number, draw_period, purchase_time) VALUES (?, ?, ?, ?, ?, ?, NULL, ?)"
    else
        SystemPrint("^1[彩票系统] ^7未知的彩票类型: " .. tostring(lotteryType))
        if callback then
            callback(false)
        end
        return false
    end
    
    -- 执行插入 (改为使用insert获取insertId)
    MySQL.Async.insert(query, params, function(insertId)
        if insertId and insertId > 0 then
            SystemPrint("^2[彩票系统] ^7直接插入彩票记录成功: " .. lotteryType .. ", ID = " .. insertId)

            -- 清除缓存
            local cacheKey = playerId .. "_" .. lotteryType
            Cache:Clear("playerTickets", cacheKey)

            if callback then
                callback(true, insertId)
            end
        else
            SystemPrint("^1[彩票系统] ^7直接插入彩票记录失败: " .. lotteryType)
            if callback then
                callback(false, nil)
            end
        end
    end)
    
    return true -- 返回执行状态，不代表插入结果
end

-- 处理彩票记录队列
function ProcessTicketQueue()
    -- 避免重复处理
    if InsertQueue.processing or #InsertQueue.tickets == 0 then
        return
    end
    
    InsertQueue.processing = true
    local ticketsToProcess = InsertQueue.tickets
    InsertQueue.tickets = {} -- 清空队列
    InsertQueue.lastInsertTime = GetGameTimer()
    
    -- 按类型分组记录
    local groupedTickets = {
        double_ball = {},
        super_lotto = {}
    }
    
    -- 分组
    for _, item in ipairs(ticketsToProcess) do
        local params = item.params
        local lotteryType = params[3] -- lottery_type参数位置
        
        if not groupedTickets[lotteryType] then
            groupedTickets[lotteryType] = {}
        end
        
        table.insert(groupedTickets[lotteryType], item)
    end
    
    -- 批量插入每种类型的记录
    for lotteryType, tickets in pairs(groupedTickets) do
        if #tickets > 0 then
            -- 获取该类型的批量大小
            local batchSize = 50 -- 默认批量大小
            if Config.InsertQueue and Config.InsertQueue.batchSize and Config.InsertQueue.batchSize[lotteryType] then
                batchSize = Config.InsertQueue.batchSize[lotteryType]
            end
            
            -- 按批次处理
            local totalTickets = #tickets
            local processedCount = 0
            
            while processedCount < totalTickets do
                -- 计算当前批次的结束位置
                local batchEnd = math.min(processedCount + batchSize, totalTickets)
                local currentBatch = {}
                
                -- 提取当前批次的记录
                for i = processedCount + 1, batchEnd do
                    table.insert(currentBatch, tickets[i])
                end
                
                -- 准备批量插入参数
                local query = ""
                local values = {}
                
                if lotteryType == "double_ball" then
                    query = "INSERT INTO lottery_tickets (player_id, player_name, lottery_type, numbers, draw_date, red_balls, blue_ball, draw_period, purchase_time) VALUES "
                    
                    local valuePlaceholders = {}
                    for i, item in ipairs(currentBatch) do
                        local params = item.params
                        table.insert(valuePlaceholders, "(?, ?, ?, ?, ?, ?, ?, NULL, ?)")
                        
                        -- 添加参数值
                        for _, value in ipairs(params) do
                            table.insert(values, value)
                        end
                    end
                    
                    query = query .. table.concat(valuePlaceholders, ", ")
                elseif lotteryType == "super_lotto" then
                    query = "INSERT INTO lottery_tickets (player_id, player_name, lottery_type, numbers, draw_date, front_balls, back_balls, draw_period, purchase_time) VALUES "

                    local valuePlaceholders = {}
                    for i, item in ipairs(currentBatch) do
                        local params = item.params
                        table.insert(valuePlaceholders, "(?, ?, ?, ?, ?, ?, ?, NULL, ?)")

                        -- 添加参数值
                        for _, value in ipairs(params) do
                            table.insert(values, value)
                        end
                    end

                    query = query .. table.concat(valuePlaceholders, ", ")
                elseif lotteryType == "arrange_five" then
                    query = "INSERT INTO lottery_tickets (player_id, player_name, lottery_type, numbers, draw_date, arrange_number, draw_period, purchase_time) VALUES "

                    local valuePlaceholders = {}
                    for i, item in ipairs(currentBatch) do
                        local params = item.params
                        table.insert(valuePlaceholders, "(?, ?, ?, ?, ?, ?, NULL, ?)")

                        -- 添加参数值
                        for _, value in ipairs(params) do
                            table.insert(values, value)
                        end
                    end

                    query = query .. table.concat(valuePlaceholders, ", ")
                end
                
                -- 执行批量插入
                if query ~= "" and #values > 0 then
                    -- 记录批次信息
                    if InsertQueue.debug then
                        SystemPrint("^3[彩票系统] ^7批量插入 " .. lotteryType .. ": 批次 " .. 
                            math.ceil(processedCount / batchSize) + 1 .. "/" .. 
                            math.ceil(totalTickets / batchSize) .. 
                            ", 记录数: " .. #currentBatch)
                    end
                    
                    MySQL.Async.execute(query, values, function(rowsChanged)
                        -- 处理回调
                        if rowsChanged and rowsChanged > 0 then
                            SystemPrint("^2[彩票系统] ^7批量插入成功: " .. lotteryType .. ", 插入 " .. rowsChanged .. " 条记录")
                            
                            -- 更新统计信息
                            UpdateQueueStats(lotteryType, rowsChanged, 1)
                            
                            -- 清除相关玩家的缓存
                            local playerIds = {}
                            for _, item in ipairs(currentBatch) do
                                local params = item.params
                                local playerId = params[1] -- player_id参数位置
                                if not playerIds[playerId] then
                                    playerIds[playerId] = true
                                    -- 清除缓存
                                    local cacheKey = playerId .. "_" .. lotteryType
                                    Cache:Clear("playerTickets", cacheKey)
                                    SystemPrint("^3[彩票系统] ^7已清除玩家彩票记录缓存: " .. cacheKey)
                                end
                            end
                            
                            -- 执行回调 (批量插入无法获取具体insertId，使用模拟ID)
                            for i, item in ipairs(currentBatch) do
                                if item.callback then
                                    -- 生成一个模拟的insertId，基于时间戳和索引
                                    local mockInsertId = os.time() * 1000 + i
                                    item.callback(true, mockInsertId)
                                end
                            end
                        else
                            SystemPrint("^1[彩票系统] ^7批量插入失败: " .. lotteryType)
                            -- 执行回调，通知失败
                            for _, item in ipairs(currentBatch) do
                                if item.callback then
                                    item.callback(false, nil)
                                end
                            end
                        end
                    end)
                end
                
                -- 更新已处理数量
                processedCount = batchEnd
                
                -- 如果还有更多批次，等待一小段时间再处理下一批
                if processedCount < totalTickets then
                    Citizen.Wait(500) -- 批次之间等待500毫秒，避免数据库压力过大
                end
            end
        end
    end
    
    InsertQueue.processing = false
end

-- 修改SaveLotteryTicket函数，使用批量插入队列 (支持回调)
function SaveLotteryTicket(playerData, lotteryType, numbers, drawDate, callback)
    if not playerData or not playerData.identifier then
        SystemPrint("^1[彩票系统] ^7SaveLotteryTicket错误: 玩家ID为空")
        if callback then callback(nil) end
        return
    end

    local encodedNumbers = json.encode(numbers)
    if not encodedNumbers then
        SystemPrint("^1[彩票系统] ^7SaveLotteryTicket错误: 无法编码numbers")
        if callback then callback(nil) end
        return
    end
    
    local redBalls, blueBall, frontBalls, backBalls, arrangeNumber = nil, nil, nil, nil, nil

    if lotteryType == 'double_ball' then
        redBalls = json.encode(numbers.redBalls)
        blueBall = numbers.blueBall
    elseif lotteryType == 'super_lotto' then
        frontBalls = json.encode(numbers.frontBalls)
        backBalls = json.encode(numbers.backBalls)
    elseif lotteryType == 'arrange_five' then
        arrangeNumber = numbers.arrangeNumber
    end
    
    -- 调试日志
    SystemPrint("^3[彩票系统] ^7保存彩票记录: 玩家=" .. playerData.name .. ", 类型=" .. lotteryType .. ", 开奖日期=" .. drawDate)
    
    -- 转换日期格式为MySQL DATETIME
    local drawDateTime = ConvertToMySQLDateTime(drawDate)
    SystemPrint("^3[彩票系统] ^7开奖日期转换为DATETIME: " .. drawDate .. " -> " .. drawDateTime)
    
    -- 获取当前时间
    local currentTime = os.date("%Y-%m-%d %H:%M:%S")
    SystemPrint("^3[彩票系统] ^7保存彩票记录 - 当前时间: " .. currentTime)
    
    -- 为了让玩家能立即查看到购票记录，先添加到缓存
    -- 创建一个模拟的票据记录，类似于数据库返回的格式
    local tempTicket = {
        id = "temp_" .. os.time() .. "_" .. math.random(1000, 9999), -- 创建一个临时ID
        player_id = playerData.identifier,
        player_name = playerData.name,
        lottery_type = lotteryType,
        numbers = encodedNumbers,
        draw_date = drawDateTime,
        purchase_time = currentTime,
        is_winning = 0, -- 默认未开奖
        is_claimed = 0, -- 默认未兑奖
        temp_record = true -- 标记为临时记录
    }
    
    if lotteryType == 'double_ball' then
        tempTicket.red_balls = redBalls
        tempTicket.blue_ball = blueBall
    elseif lotteryType == 'super_lotto' then
        tempTicket.front_balls = frontBalls
        tempTicket.back_balls = backBalls
    elseif lotteryType == 'arrange_five' then
        tempTicket.arrange_number = arrangeNumber
    end
    
    -- 添加到缓存
    local cacheKey = playerData.identifier .. "_" .. lotteryType
    local existingTickets = Cache:Get("playerTickets", cacheKey) or {}
    table.insert(existingTickets, 1, tempTicket) -- 插入到数组开头
    Cache:Set("playerTickets", cacheKey, existingTickets)
    SystemPrint("^2[彩票系统] ^7临时彩票记录已添加到缓存: " .. cacheKey)
    
    -- 准备SQL参数
    SystemPrint("^3[彩票系统] ^7SQL参数 - " .. lotteryType .. ":")
    SystemPrint("^3[彩票系统] ^7  player_id = " .. playerData.identifier)
    SystemPrint("^3[彩票系统] ^7  player_name = " .. playerData.name)
    SystemPrint("^3[彩票系统] ^7  lottery_type = " .. lotteryType)
    SystemPrint("^3[彩票系统] ^7  draw_date = " .. drawDateTime)
    SystemPrint("^3[彩票系统] ^7  purchase_time = " .. currentTime)
    
    if lotteryType == 'double_ball' then
        -- 使用队列系统进行批量插入
        AddToInsertQueue({
            tostring(playerData.identifier),
            tostring(playerData.name or "未知玩家"),
            tostring(lotteryType),
            encodedNumbers,
            drawDateTime,
            redBalls,
            blueBall,
            currentTime
        }, function(success, insertId)
            if success then
                -- 更新奖池和彩票店账户
                -- 使用资金流向配置计算金额
                local priceToUse = Config.DoubleBall.price
                local prizePoolContribution = math.floor(priceToUse * Config.MoneyFlow.lottery.prizePool)
                local shopAccountContribution = math.floor(priceToUse * Config.MoneyFlow.lottery.shopAccount)

                -- 更新奖池
                UpdatePrizePool(lotteryType, priceToUse)
                -- 更新彩票店账户
                UpdateShopAccount(shopAccountContribution)
                -- 成功后清除缓存，让下次查询直接从数据库获取
                Cache:Clear("playerTickets", cacheKey)

                SystemPrint("^2[彩票系统] ^7彩票记录保存成功: ID = " .. tostring(insertId))
                if callback then callback(insertId) end
            else
                SystemPrint("^1[彩票系统] ^7彩票记录保存失败: " .. lotteryType)
                if callback then callback(nil) end
            end
        end)
    elseif lotteryType == 'super_lotto' then
        -- 使用队列系统进行批量插入
        AddToInsertQueue({
            tostring(playerData.identifier),
            tostring(playerData.name or "未知玩家"),
            tostring(lotteryType),
            encodedNumbers,
            drawDateTime,
            frontBalls,
            backBalls,
            currentTime
        }, function(success, insertId)
            if success then
                -- 更新奖池和彩票店账户
                -- 使用资金流向配置计算金额
                local priceToUse = Config.SuperLotto.price
                local prizePoolContribution = math.floor(priceToUse * Config.MoneyFlow.lottery.prizePool)
                local shopAccountContribution = math.floor(priceToUse * Config.MoneyFlow.lottery.shopAccount)

                -- 更新奖池
                UpdatePrizePool(lotteryType, priceToUse)
                -- 更新彩票店账户
                UpdateShopAccount(shopAccountContribution)
                -- 成功后清除缓存，让下次查询直接从数据库获取
                Cache:Clear("playerTickets", cacheKey)

                SystemPrint("^2[彩票系统] ^7彩票记录保存成功: ID = " .. tostring(insertId))
                if callback then callback(insertId) end
            else
                SystemPrint("^1[彩票系统] ^7彩票记录保存失败: " .. lotteryType)
                if callback then callback(nil) end
            end
        end)
    elseif lotteryType == 'arrange_five' then
        -- 使用队列系统进行批量插入
        AddToInsertQueue({
            tostring(playerData.identifier),
            tostring(playerData.name or "未知玩家"),
            tostring(lotteryType),
            encodedNumbers,
            drawDateTime,
            arrangeNumber,
            currentTime
        }, function(success, insertId)
            if success then
                -- 更新奖池和彩票店账户
                -- 使用资金流向配置计算金额
                local priceToUse = Config.ArrangeFive.price
                local prizePoolContribution = math.floor(priceToUse * Config.MoneyFlow.lottery.prizePool)
                local shopAccountContribution = math.floor(priceToUse * Config.MoneyFlow.lottery.shopAccount)

                -- 更新奖池
                UpdatePrizePool(lotteryType, priceToUse)
                -- 更新彩票店账户
                UpdateShopAccount(shopAccountContribution)
                -- 成功后清除缓存，让下次查询直接从数据库获取
                Cache:Clear("playerTickets", cacheKey)

                SystemPrint("^2[彩票系统] ^7彩票记录保存成功: ID = " .. tostring(insertId))
                if callback then callback(insertId) end
            else
                SystemPrint("^1[彩票系统] ^7彩票记录保存失败: " .. lotteryType)
                if callback then callback(nil) end
            end
        end)
    end

    -- 如果没有匹配的彩票类型，返回错误
    if lotteryType ~= 'double_ball' and lotteryType ~= 'super_lotto' and lotteryType ~= 'arrange_five' then
        SystemPrint("^1[彩票系统] ^7SaveLotteryTicket错误: 未知的彩票类型 " .. lotteryType)
        if callback then callback(nil) end
    end
end

-- 保存刮刮乐购买记录 (优化为异步)
function SaveScratchCard(playerData, cardType, cardName, price, ticketData, callback)
    if not playerData or not playerData.identifier then
        SystemPrint("^1[彩票系统] ^7SaveScratchCard错误: 玩家ID为空")
        if callback then callback(nil) end
        return
    end

    DebugPrint("^3[彩票系统] ^7尝试保存刮刮乐记录: 玩家ID = " .. playerData.identifier .. ", 卡类型 = " .. cardType)

    local encodedData = json.encode(ticketData)
    if not encodedData then
        SystemPrint("^1[彩票系统] ^7SaveScratchCard错误: 无法编码ticket_data")
        if callback then callback(nil) end
        return
    end

    -- 改为异步插入，避免阻塞
    MySQL.Async.insert('INSERT INTO scratch_cards (player_id, player_name, card_type, card_name, purchase_price, ticket_data) VALUES (?, ?, ?, ?, ?, ?)', {
        playerData.identifier,
        playerData.name or "未知玩家",
        cardType,
        cardName,
        price,
        encodedData
    }, function(insertId)
        if not insertId or insertId <= 0 then
            SystemPrint("^1[彩票系统] ^7SaveScratchCard错误: 插入失败，返回ID = " .. tostring(insertId))
            if callback then callback(nil) end
        else
            DebugPrint("^2[彩票系统] ^7刮刮乐记录保存成功: ID = " .. tostring(insertId))
            if callback then callback(insertId) end
        end
    end)
end

-- 更新刮刮乐结果
function UpdateScratchResult(ticketId, prizeAmount, ticketData)
    if ticketData then
        -- 如果提供了ticketData，同时更新ticket_data字段
        local encodedData = json.encode(ticketData)
        return MySQL.Async.execute('UPDATE scratch_cards SET is_scratched = 1, prize_amount = ?, ticket_data = ?, scratch_time = NOW() WHERE id = ?', {
            prizeAmount, encodedData, ticketId
        })
    else
        -- 如果没有提供ticketData，只更新基本信息
        return MySQL.Async.execute('UPDATE scratch_cards SET is_scratched = 1, prize_amount = ?, scratch_time = NOW() WHERE id = ?', {
            prizeAmount, ticketId
        })
    end
end

-- 兑奖 (优化为异步)
function ClaimPrize(ticketId, lotteryType, callback)
    local table_name = lotteryType == 'scratch' and 'scratch_cards' or 'lottery_tickets'

    -- 改为异步执行，避免阻塞
    MySQL.Async.execute('UPDATE ' .. table_name .. ' SET is_claimed = 1, claimed_time = NOW() WHERE id = ?', {ticketId}, function(rowsChanged)
        local success = rowsChanged and rowsChanged > 0

        -- 记录日志
        if success then
            DebugPrint("^2票ID " .. ticketId .. " 已成功标记为已兑奖")

            -- 立即清除相关缓存
            if Cache then
                -- 清除未兑奖奖品缓存
                Cache:Clear("unclaimedPrizes", "unclaimed_*")
                -- 清除玩家彩票缓存
                Cache:Clear("playerTickets", "*")
            end

            -- 清除管理系统相关缓存
            exports[GetCurrentResourceName()]:ClearCache("winningRecords")
            exports[GetCurrentResourceName()]:ClearCache("claimRecords")
            exports[GetCurrentResourceName()]:ClearCache("unclaimedRecords")
        else
            SystemPrint("^1[彩票系统] ^7票ID " .. ticketId .. " 兑奖失败")
        end

        if callback then callback(success) end
    end)
end

-- 获取玩家彩票统计 (异步版本)
function GetPlayerLotteryStats(playerIdentifier, lotteryType, callback)
    -- 如果是刮刮乐，直接查询
    if lotteryType == 'scratch' then
        DebugPrint("^3[彩票系统] ^7查询玩家刮刮乐统计 - 玩家ID: " .. playerIdentifier)

        -- 查询刮刮乐统计
        local query = 'SELECT card_type, COUNT(*) as count, IFNULL(SUM(prize_amount), 0) as total_prize FROM scratch_cards WHERE player_id = ? GROUP BY card_type'

        MySQL.Async.fetchAll(query, {playerIdentifier}, function(result)
            result = result or {}

            -- 确保total_prize不为null并转换为数字
            for i, record in ipairs(result) do
                if record.total_prize == nil then
                    record.total_prize = 0
                end
                -- 确保total_prize是数字类型，而不是字符串
                if type(record.total_prize) == "string" then
                    record.total_prize = tonumber(record.total_prize) or 0
                end
            end

            DebugPrint("^2[彩票系统] ^7找到 " .. #result .. " 条刮刮乐记录")

            if callback then callback(result) end
        end)
        return
    else
        -- 其他类型的彩票使用缓存
        local cacheKey = playerIdentifier .. "_" .. lotteryType

        -- 尝试从缓存获取
        if Cache then
            local cachedResult = Cache:Get("playerTickets", cacheKey)
            if cachedResult then
                -- 确保数据格式正确
                for i, record in ipairs(cachedResult) do
                    if record.prize_amount == nil then
                        record.prize_amount = 0
                    end
                    if type(record.prize_amount) == "string" then
                        record.prize_amount = tonumber(record.prize_amount) or 0
                    end
                end

                if callback then callback(cachedResult) end
                return
            end
        end

        DebugPrint("^3[彩票系统] ^7查询玩家彩票统计 - 玩家ID: " .. playerIdentifier .. ", 类型: " .. lotteryType)

        -- 查询彩票记录
        local query = [[
            SELECT
                id,
                lottery_type,
                numbers,
                red_balls,
                blue_ball,
                front_balls,
                back_balls,
                arrange_number,
                purchase_time,
                draw_date,
                draw_period,
                is_winning,
                prize_amount,
                reward_data
            FROM
                lottery_tickets
            WHERE
                player_id = ? AND
                lottery_type = ?
            ORDER BY
                purchase_time DESC
            LIMIT 50
        ]]

        MySQL.Async.fetchAll(query, {playerIdentifier, lotteryType}, function(result)
            result = result or {}

            -- 确保数据格式正确
            for i, record in ipairs(result) do
                if record.prize_amount == nil then
                    record.prize_amount = 0
                end
                if type(record.prize_amount) == "string" then
                    record.prize_amount = tonumber(record.prize_amount) or 0
                end
            end

            DebugPrint("^2[彩票系统] ^7找到 " .. #result .. " 条彩票记录")

            -- 保存到缓存
            if Cache then
                Cache:Set("playerTickets", cacheKey, result)
            end

            if callback then callback(result) end
        end)
    end
end

-- 获取未兑奖奖品 (异步版本)
function GetUnclaimedPrizes(playerIdentifier, callback)
    DebugPrint("^3[彩票系统] ^7获取未兑奖奖品 - 玩家ID: " .. playerIdentifier)

    -- 生成缓存键
    local cacheKey = "unclaimed_" .. playerIdentifier

    -- 尝试从缓存获取
    if Cache then
        local cachedResult = Cache:Get("unclaimedPrizes", cacheKey)
        if cachedResult then
            if callback then callback(cachedResult) end
            return
        end
    end

    -- 异步查询刮刮乐奖品
    MySQL.Async.fetchAll(
        'SELECT id, card_type, card_name as lottery_type, prize_amount, purchase_time FROM scratch_cards WHERE player_id = ? AND prize_amount > 0 AND is_claimed = 0',
        {playerIdentifier},
        function(scratchPrizes)
            scratchPrizes = scratchPrizes or {}

            -- 异步查询彩票奖品
            MySQL.Async.fetchAll(
                'SELECT id, lottery_type, prize_amount, purchase_time FROM lottery_tickets WHERE player_id = ? AND is_winning = 1 AND prize_amount > 0 AND is_claimed = 0',
                {playerIdentifier},
                function(lotteryPrizes)
                    lotteryPrizes = lotteryPrizes or {}

                    DebugPrint("^3[彩票系统] ^7找到刮刮乐未兑奖: " .. #scratchPrizes .. "张, 彩票未兑奖: " .. #lotteryPrizes .. "张")

                    -- 合并结果
                    local allPrizes = {}

                    for _, prize in pairs(scratchPrizes) do
                        prize.card_type = prize.card_type -- 标记为刮刮乐
                        table.insert(allPrizes, prize)
                    end

                    for _, prize in pairs(lotteryPrizes) do
                        table.insert(allPrizes, prize)
                    end

                    -- 保存到缓存
                    if Cache then
                        Cache:Set("unclaimedPrizes", cacheKey, allPrizes)
                    end

                    if callback then callback(allPrizes) end
                end
            )
        end
    )
end

-- 获取开奖历史 (异步版本)
function GetDrawHistory(lotteryType, limit, callback)
    limit = limit or 10

    -- 生成缓存键
    local cacheKey = lotteryType .. "_" .. tostring(limit)

    -- 尝试从缓存获取
    if Cache then
        local cachedResult = Cache:Get("drawHistory", cacheKey)
        if cachedResult then
            if callback then callback(cachedResult) end
            return
        end
    end

    -- 添加调试信息
    DebugPrint("^3[彩票系统] ^7GetDrawHistory函数 - 类型: " .. lotteryType .. ", 限制: " .. limit)

    -- 使用异步查询，按ID降序排序确保最新记录在前
    MySQL.Async.fetchAll(
        'SELECT * FROM draw_history WHERE lottery_type = ? ORDER BY id DESC, draw_date DESC LIMIT ?',
        {lotteryType, limit},
        function(result)
            result = result or {}

            DebugPrint("^3[彩票系统] ^7GetDrawHistory查询结果: 找到 " .. #result .. " 条记录")

            -- 保存到缓存
            if Cache then
                Cache:Set("drawHistory", cacheKey, result)
            end

            if callback then callback(result) end
        end
    )
end

-- 保存开奖结果 (简化版本)
function SaveDrawResult(lotteryType, periodNumber, drawDate, winningNumbers, salesData, callback)
    if not winningNumbers then
        SystemPrint("^1[彩票系统] ^7SaveDrawResult错误: 中奖号码为空")
        if callback then callback(nil) end
        return
    end

    local encodedWinningNumbers = json.encode(winningNumbers)
    if not encodedWinningNumbers then
        SystemPrint("^1[彩票系统] ^7SaveDrawResult错误: 无法编码中奖号码")
        if callback then callback(nil) end
        return
    end

    -- 使用简单的期号格式
    local finalPeriodNumber = periodNumber or (lotteryType .. "-" .. os.date("%Y%m%d"))

    -- 格式化日期 - 使用实际的开奖时间
    local drawDateFormatted = drawDate
    if type(drawDate) == "string" and not string.find(drawDate, ":") then
        -- 使用当前时间作为开奖时间，而不是固定的20:00:00
        drawDateFormatted = drawDate .. " " .. os.date("%H:%M:%S")
    end

    -- 编码奖品详情
    local encodedPrizeDetails = json.encode(salesData.prizeDetails or {})

    -- 根据彩票类型插入数据
    local insertId = nil
    if lotteryType == 'double_ball' then
        local redBalls = json.encode(winningNumbers.redBalls or {})
        local blueBall = winningNumbers.blueBall or 0

        MySQL.Async.insert('INSERT INTO draw_history (lottery_type, period_number, draw_date, winning_numbers, total_sales, total_winners, prize_details, jackpot_amount, red_balls, blue_ball) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', {
            lotteryType,
            finalPeriodNumber,
            drawDateFormatted,
            encodedWinningNumbers,
            salesData.totalSales or 0,
            salesData.totalWinners or 0,
            encodedPrizeDetails,
            salesData.jackpotAmount or 0,
            redBalls,
            blueBall
        }, function(id)
            if id and id > 0 then
                DebugPrint("^2[彩票系统] ^7开奖结果保存成功: ID = " .. tostring(id) .. ", 期号: " .. finalPeriodNumber)
                if callback then callback(id) end
            else
                SystemPrint("^1[彩票系统] ^7保存开奖结果失败: 数据库插入返回无效ID")
                if callback then callback(nil) end
            end
        end)
    elseif lotteryType == 'super_lotto' then
        local frontBalls = json.encode(winningNumbers.frontBalls or {})
        local backBalls = json.encode(winningNumbers.backBalls or {})

        MySQL.Async.insert('INSERT INTO draw_history (lottery_type, period_number, draw_date, winning_numbers, total_sales, total_winners, prize_details, jackpot_amount, front_balls, back_balls) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', {
            lotteryType,
            finalPeriodNumber,
            drawDateFormatted,
            encodedWinningNumbers,
            salesData.totalSales or 0,
            salesData.totalWinners or 0,
            encodedPrizeDetails,
            salesData.jackpotAmount or 0,
            frontBalls,
            backBalls
        }, function(id)
            if id and id > 0 then
                DebugPrint("^2[彩票系统] ^7开奖结果保存成功: ID = " .. tostring(id) .. ", 期号: " .. finalPeriodNumber)
                if callback then callback(id) end
            else
                SystemPrint("^1[彩票系统] ^7保存开奖结果失败: 数据库插入返回无效ID")
                if callback then callback(nil) end
            end
        end)
    elseif lotteryType == 'arrange_five' then
        local arrangeNumber = winningNumbers.arrangeNumber or ""

        MySQL.Async.insert('INSERT INTO draw_history (lottery_type, period_number, draw_date, winning_numbers, total_sales, total_winners, prize_details, jackpot_amount, arrange_number) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)', {
            lotteryType,
            finalPeriodNumber,
            drawDateFormatted,
            encodedWinningNumbers,
            salesData.totalSales or 0,
            salesData.totalWinners or 0,
            encodedPrizeDetails,
            salesData.jackpotAmount or 0,
            arrangeNumber
        }, function(id)
            if id and id > 0 then
                DebugPrint("^2[彩票系统] ^7开奖结果保存成功: ID = " .. tostring(id) .. ", 期号: " .. finalPeriodNumber)
                if callback then callback(id) end
            else
                SystemPrint("^1[彩票系统] ^7保存开奖结果失败: 数据库插入返回无效ID")
                if callback then callback(nil) end
            end
        end)
    else
        MySQL.Async.insert('INSERT INTO draw_history (lottery_type, period_number, draw_date, winning_numbers, total_sales, total_winners, prize_details, jackpot_amount) VALUES (?, ?, ?, ?, ?, ?, ?, ?)', {
            lotteryType,
            finalPeriodNumber,
            drawDateFormatted,
            encodedWinningNumbers,
            salesData.totalSales or 0,
            salesData.totalWinners or 0,
            encodedPrizeDetails,
            salesData.jackpotAmount or 0
        }, function(id)
            if id and id > 0 then
                DebugPrint("^2[彩票系统] ^7开奖结果保存成功: ID = " .. tostring(id) .. ", 期号: " .. finalPeriodNumber)
                if callback then callback(id) end
            else
                SystemPrint("^1[彩票系统] ^7保存开奖结果失败: 数据库插入返回无效ID")
                if callback then callback(nil) end
            end
        end)
    end
end



-- 本地中奖检查函数（如果全局CheckWinning不可用时使用）
local function LocalCheckWinning(lotteryType, playerNumbers, winningNumbers)
    if not CheckWinning then
        -- 如果全局CheckWinning函数不存在，使用本地简化版本
        local config = nil
        if lotteryType == 'double_ball' then
            config = Config.DoubleBall
        elseif lotteryType == 'super_lotto' then
            config = Config.SuperLotto
        elseif lotteryType == 'arrange_five' then
            config = Config.ArrangeFive
        end

        if not config or not winningNumbers or not playerNumbers then
            return 0, 0
        end

        if lotteryType == 'double_ball' then
            local redMatches = 0
            local blueMatch = 0

            if playerNumbers.redBalls and winningNumbers.redBalls and
               playerNumbers.blueBall and winningNumbers.blueBall then

                -- 计算红球匹配数
                for _, playerRed in pairs(playerNumbers.redBalls) do
                    for _, winningRed in pairs(winningNumbers.redBalls) do
                        if playerRed == winningRed then
                            redMatches = redMatches + 1
                            break
                        end
                    end
                end

                -- 计算蓝球匹配
                if playerNumbers.blueBall == winningNumbers.blueBall then
                    blueMatch = 1
                end

                -- 查找奖项
                for level, prize in pairs(config.prizes) do
                    if prize.match and prize.match[1] == redMatches and prize.match[2] == blueMatch then
                        local prizeAmount = prize.amount or 0
                        return level, prizeAmount
                    end
                end
            end
        elseif lotteryType == 'super_lotto' then
            local frontMatches = 0
            local backMatches = 0

            if playerNumbers.frontBalls and winningNumbers.frontBalls and
               playerNumbers.backBalls and winningNumbers.backBalls then

                -- 计算前区球匹配数
                for _, playerFront in pairs(playerNumbers.frontBalls) do
                    for _, winningFront in pairs(winningNumbers.frontBalls) do
                        if playerFront == winningFront then
                            frontMatches = frontMatches + 1
                            break
                        end
                    end
                end

                -- 计算后区球匹配数
                for _, playerBack in pairs(playerNumbers.backBalls) do
                    for _, winningBack in pairs(winningNumbers.backBalls) do
                        if playerBack == winningBack then
                            backMatches = backMatches + 1
                            break
                        end
                    end
                end

                -- 查找奖项
                for level, prize in pairs(config.prizes) do
                    if prize.match and prize.match[1] == frontMatches and prize.match[2] == backMatches then
                        local prizeAmount = prize.amount or 0
                        return level, prizeAmount
                    end
                end
            end
        elseif lotteryType == 'arrange_five' then
            -- 确保号码格式正确
            if playerNumbers.arrangeNumber and winningNumbers.arrangeNumber then
                -- 排列5只有完全匹配才中奖
                if playerNumbers.arrangeNumber == winningNumbers.arrangeNumber then
                    -- 排列5只有一个奖项
                    local prize = config.prizes[1]
                    if prize then
                        -- 排列5使用物品奖励，这里返回1表示中奖
                        return 1, 1
                    end
                end
            end
        end

        return 0, 0
    else
        -- 使用全局CheckWinning函数
        return CheckWinning(lotteryType, playerNumbers, winningNumbers)
    end
end

-- 计算中奖结果
function CalculateWinningTickets(lotteryType, winningNumbers, periodNumber, isManualDraw, callback)
    DebugPrint("^2[彩票系统] ^7开始计算中奖结果 - 彩票类型: " .. lotteryType .. ", 期号: " .. periodNumber)

    local query = 'SELECT * FROM lottery_tickets WHERE lottery_type = ? AND (draw_period IS NULL OR draw_period = "")'
    local params = {lotteryType}

    -- 异步查询彩票
    MySQL.Async.fetchAll(query, params, function(tickets)
        tickets = tickets or {}
        DebugPrint("^3[彩票系统] ^7查询到符合条件的彩票数量: " .. #tickets)

        if #tickets == 0 then
            DebugPrint("^3[彩票系统] ^7没有找到符合条件的彩票")
            if callback then callback({totalWinners = 0, winners = {}}) end
            return
        end

        -- 真正的中奖计算逻辑
        local winners = {}
        local totalWinners = 0
        local processedCount = 0

        for _, ticket in ipairs(tickets) do
            -- 解析玩家号码
            local playerNumbers = nil

            if lotteryType == 'double_ball' then
                -- 双色球号码解析
                if ticket.red_balls and ticket.blue_ball then
                    -- 从数据库字段直接获取
                    local redBalls = {}
                    if type(ticket.red_balls) == "string" then
                        for num in ticket.red_balls:gmatch("%d+") do
                            table.insert(redBalls, tonumber(num))
                        end
                    else
                        redBalls = ticket.red_balls
                    end

                    playerNumbers = {
                        redBalls = redBalls,
                        blueBall = tonumber(ticket.blue_ball)
                    }
                end
            elseif lotteryType == 'super_lotto' then
                -- 大乐透号码解析
                if ticket.front_balls and ticket.back_balls then
                    local frontBalls = {}
                    local backBalls = {}

                    if type(ticket.front_balls) == "string" then
                        for num in ticket.front_balls:gmatch("%d+") do
                            table.insert(frontBalls, tonumber(num))
                        end
                    else
                        frontBalls = ticket.front_balls
                    end

                    if type(ticket.back_balls) == "string" then
                        for num in ticket.back_balls:gmatch("%d+") do
                            table.insert(backBalls, tonumber(num))
                        end
                    else
                        backBalls = ticket.back_balls
                    end

                    playerNumbers = {
                        frontBalls = frontBalls,
                        backBalls = backBalls
                    }
                end
            elseif lotteryType == 'arrange_five' then
                -- 排列5号码解析
                if ticket.arrange_number then
                    playerNumbers = {
                        arrangeNumber = ticket.arrange_number
                    }
                end
            end

            -- 检查中奖
            if playerNumbers then
                local prizeLevel, prizeAmount = LocalCheckWinning(lotteryType, playerNumbers, winningNumbers)

                if prizeLevel > 0 and prizeAmount > 0 then
                    -- 中奖了
                    totalWinners = totalWinners + 1

                    -- 排列5特殊处理：发放物品奖励
                    if lotteryType == 'arrange_five' then
                        -- 根据配置选择物品奖励
                        local config = Config.ArrangeFive
                        if config and config.prizes and config.prizes[1] and config.prizes[1].itemRewards then
                            local itemRewards = config.prizes[1].itemRewards
                            local totalWeight = 0
                            local items = {}
                            local weights = {}

                            -- 计算总权重
                            for item, weight in pairs(itemRewards) do
                                table.insert(items, item)
                                table.insert(weights, weight)
                                totalWeight = totalWeight + weight
                            end

                            -- 按权重随机选择物品
                            local randomValue = math.random() * totalWeight
                            local currentWeight = 0
                            local selectedItem = items[1] or "bread" -- 默认物品

                            for i = 1, #items do
                                currentWeight = currentWeight + weights[i]
                                if randomValue <= currentWeight then
                                    selectedItem = items[i]
                                    break
                                end
                            end

                            -- 解析物品和数量
                            local itemName = selectedItem
                            local itemAmount = 1
                            if string.find(selectedItem, ":") then
                                local parts = {}
                                for part in string.gmatch(selectedItem, "([^:]+)") do
                                    table.insert(parts, part)
                                end
                                if #parts == 2 then
                                    itemName = parts[1]
                                    itemAmount = tonumber(parts[2]) or 1
                                end
                            end

                            -- 将物品信息存储到数据库中，供兑奖时使用
                            local rewardData = json.encode({
                                item = itemName,
                                amount = itemAmount,
                                displayName = selectedItem
                            })

                            MySQL.Async.execute('UPDATE lottery_tickets SET draw_period = ?, is_winning = 1, prize_level = ?, prize_amount = ?, reward_data = ? WHERE id = ?', {
                                periodNumber, prizeLevel, 1, rewardData, ticket.id
                            })

                            SystemPrint(string.format("^2[彩票系统] ^7排列5中奖彩票 - ID: %s, 玩家: %s, 奖品: %s x%d",
                                ticket.id, ticket.player_name or "未知", itemName, itemAmount))
                        else
                            -- 配置错误，使用默认处理
                            MySQL.Async.execute('UPDATE lottery_tickets SET draw_period = ?, is_winning = 1, prize_level = ?, prize_amount = ? WHERE id = ?', {
                                periodNumber, prizeLevel, prizeAmount, ticket.id
                            })
                        end
                    else
                        -- 其他彩票类型的正常处理
                        MySQL.Async.execute('UPDATE lottery_tickets SET draw_period = ?, is_winning = 1, prize_level = ?, prize_amount = ? WHERE id = ?', {
                            periodNumber, prizeLevel, prizeAmount, ticket.id
                        })
                    end

                    -- 按奖级分组
                    if not winners[prizeLevel] then
                        winners[prizeLevel] = {}
                    end
                    table.insert(winners[prizeLevel], {
                        ticketId = ticket.id,
                        playerId = ticket.player_id,
                        playerName = ticket.player_name,
                        prizeAmount = prizeAmount
                    })

                    if lotteryType ~= 'arrange_five' then
                        SystemPrint(string.format("^2[彩票系统] ^7中奖彩票 - ID: %s, 玩家: %s, 奖级: %d, 奖金: %d",
                            ticket.id, ticket.player_name or "未知", prizeLevel, prizeAmount))
                    end
                else
                    -- 未中奖
                    MySQL.Async.execute('UPDATE lottery_tickets SET draw_period = ?, is_winning = 0, prize_level = 0, prize_amount = 0 WHERE id = ?', {
                        periodNumber, ticket.id
                    })
                end
            else
                -- 号码格式错误，标记为未中奖
                SystemPrint("^1[彩票系统] ^7彩票号码格式错误 - ID: " .. ticket.id)
                MySQL.Async.execute('UPDATE lottery_tickets SET draw_period = ?, is_winning = 0, prize_level = 0, prize_amount = 0 WHERE id = ?', {
                    periodNumber, ticket.id
                })
            end

            processedCount = processedCount + 1
        end

        SystemPrint("^2[彩票系统] ^7中奖计算完成，处理了 " .. processedCount .. " 张彩票，共有 " .. totalWinners .. " 张中奖彩票")

        if callback then
            callback({
                totalWinners = totalWinners,
                winners = winners
            })
        end
    end)
end


-- 保存中奖公告 (简化版本)
function SaveAnnouncement(playerName, lotteryType, prizeAmount, announcementText, callback)
    if not playerName or not lotteryType or not prizeAmount or not announcementText then
        SystemPrint("^1[彩票系统] ^7SaveAnnouncement错误: 参数不完整")
        if callback then callback(nil) end
        return
    end

    MySQL.Async.insert('INSERT INTO lottery_announcements (player_name, lottery_type, prize_amount, announcement_text) VALUES (?, ?, ?, ?)', {
        playerName,
        lotteryType,
        prizeAmount,
        announcementText
    }, function(insertId)
        if not insertId or insertId <= 0 then
            SystemPrint("^1[彩票系统] ^7SaveAnnouncement错误: 插入失败，返回ID = " .. tostring(insertId))
            if callback then callback(nil) end
        else
            SystemPrint("^2[彩票系统] ^7中奖公告保存成功: ID = " .. tostring(insertId))
            if callback then callback(insertId) end
        end
    end)
end

-- 将日期字符串转换为MySQL DATETIME格式
function ConvertToMySQLDateTime(dateStr)
    -- 尝试解析日期字符串
    local year, month, day = string.match(dateStr, "(%d+)-(%d+)-(%d+)")
    if year and month and day then
        -- 使用默认时间 20:00:00
        return string.format("%s-%s-%s 20:00:00", year, month, day)
    else
        -- 无法解析，使用当前日期和时间
        SystemPrint("^1[彩票系统] ^7无法解析开奖日期: " .. dateStr)
        return os.date("%Y-%m-%d %H:%M:%S")
    end
end