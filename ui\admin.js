// 彩票店管理系统 JavaScript

// 全局变量
let adminData = null;
let currentFilter = 'all';
let currentChartFilter = 'all';
let salesChart = null;

// 调试模式控制 - 设置为false可以关闭所有调试输出
const DEBUG_MODE = false;

// 重写console.log和console.error来控制调试输出
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

console.log = function(...args) {
    if (DEBUG_MODE) {
        originalConsoleLog.apply(console, args);
    }
};

console.error = function(...args) {
    if (DEBUG_MODE) {
        originalConsoleError.apply(console, args);
    }
};

// 为了兼容性，定义debugMode变量
let debugMode = DEBUG_MODE;

// 用于防抖通知的计时器和缓存
let notificationTimer = null;
let lastNotification = {
    message: '',
    type: '',
    time: 0
};

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 关闭按钮
    document.getElementById('close-admin').addEventListener('click', closeAdminSystem);
    
    // 导航按钮
    const navButtons = document.querySelectorAll('.admin-nav .nav-btn');
    navButtons.forEach(button => {
        button.addEventListener('click', function() {
            switchTab(this.dataset.tab);
        });
    });
    
    // 彩票类型标签页按钮
    const lotteryTabButtons = document.querySelectorAll('#lottery-config-tab .record-filter .filter-btn');
    lotteryTabButtons.forEach(button => {
        button.addEventListener('click', function() {
            switchLotteryTab(this.dataset.lotteryTab);
        });
    });
    
    // 销售图表过滤器
    const chartFilters = document.querySelectorAll('.chart-filters .chart-filter');
    chartFilters.forEach(filter => {
        filter.addEventListener('click', function() {
            setChartFilter(this.dataset.filter);
        });
    });
    
    // 记录过滤器
    const recordFilters = document.querySelectorAll('.record-filter .filter-btn');
    recordFilters.forEach(filter => {
        filter.addEventListener('click', function() {
            if (this.parentElement.closest('#lottery-config-tab')) return; // 跳过彩票配置标签页的按钮
            setRecordFilter(this.dataset.filter, this.closest('.tab-content').id);
        });
    });
    
    // 配置按钮
    document.getElementById('refresh-config-btn').addEventListener('click', getLotteryConfig);
    document.getElementById('save-all-config-btn').addEventListener('click', saveAllLotteryConfig);
    document.getElementById('save-double-ball-btn').addEventListener('click', saveDoubleBallConfig);
    document.getElementById('save-super-lotto-btn').addEventListener('click', saveSuperLottoConfig);
    document.getElementById('save-arrange-five-btn').addEventListener('click', saveArrangeFiveConfig);
    
    // 奖项配置按钮 - 使用事件委托，因为按钮是动态生成的
    document.addEventListener('click', function(e) {
        // 只处理按钮点击，避免干扰输入框
        if (e.target.tagName !== 'BUTTON' && !e.target.closest('button')) {
            return;
        }

        console.log('按钮点击事件:', e.target.id, e.target.className, e.target.textContent);

        if (e.target.id === 'save-double-ball-prizes-btn') {
            console.log('✅ 检测到双色球奖项保存按钮点击');
            saveDoubleBallPrizes();
        } else if (e.target.id === 'save-super-lotto-prizes-btn') {
            console.log('✅ 检测到大乐透奖项保存按钮点击');
            saveSuperLottoPrizes();
        } else if (e.target.textContent && e.target.textContent.includes('保存奖项配置')) {
            console.log('🔍 检测到包含"保存奖项配置"文本的按钮点击');
            if (e.target.closest('#double-ball-tab')) {
                console.log('🎯 在双色球标签页中，调用双色球奖项保存');
                saveDoubleBallPrizes();
            } else if (e.target.closest('#super-lotto-tab')) {
                console.log('🎯 在大乐透标签页中，调用大乐透奖项保存');
                saveSuperLottoPrizes();
            }
        }
    });
    
    // 刮刮乐配置按钮
    document.getElementById('save-scratch-xixiangfeng-btn').addEventListener('click', saveXixiangfengConfig);
    document.getElementById('save-scratch-fusong-btn').addEventListener('click', saveFusongConfig);
    document.getElementById('save-scratch-yaocai-btn').addEventListener('click', saveYaocaiConfig);
    document.getElementById('save-scratch-caizuan-btn').addEventListener('click', saveCaizuanConfig);
    document.getElementById('save-scratch-zhongguofu-btn').addEventListener('click', saveZhongguofuConfig);
    document.getElementById('save-scratch-chengfeng-btn').addEventListener('click', saveChengfengConfig);

    // 刮刮乐权重配置按钮
    document.getElementById('save-scratch-xixiangfeng-amount-rates-btn').addEventListener('click', saveXixiangfengAmountRates);
    document.getElementById('save-scratch-fusong-row-amount-rates-btn').addEventListener('click', saveFusongRowAmountRates);
    document.getElementById('save-scratch-fusong-match-rates-btn').addEventListener('click', saveFusongMatchRates);
    document.getElementById('save-scratch-yaocai-winning-amount-rates-btn').addEventListener('click', saveYaocaiWinningAmountRates);
    document.getElementById('save-scratch-yaocai-match-rates-btn').addEventListener('click', saveYaocaiMatchRates);
    document.getElementById('save-scratch-caizuan-winning-item-rates-btn').addEventListener('click', saveCaizuanWinningItemRates);
    document.getElementById('save-scratch-caizuan-match-rates-btn').addEventListener('click', saveCaizuanMatchRates);
    document.getElementById('save-scratch-caizuan-diamond-rates-btn').addEventListener('click', saveCaizuanDiamondRates);
    document.getElementById('save-scratch-zhongguofu-winning-item-rates-btn').addEventListener('click', saveZhongguofuWinningItemRates);
    document.getElementById('save-scratch-zhongguofu-match-rates-btn').addEventListener('click', saveZhongguofuMatchRates);
    document.getElementById('save-scratch-zhongguofu-fu-rates-btn').addEventListener('click', saveZhongguofuFuRates);
    document.getElementById('save-scratch-chengfeng-winning-item-rates-btn').addEventListener('click', saveChengfengWinningItemRates);
    document.getElementById('save-scratch-chengfeng-icon-item-rates-btn').addEventListener('click', saveChengfengIconItemRates);
    document.getElementById('save-scratch-chengfeng-sail-rates-btn').addEventListener('click', saveChengfengSailRates);
    document.getElementById('save-scratch-chengfeng-tornado-rates-btn').addEventListener('click', saveChengfengTornadoRates);

    // 排列5物品配置按钮
    document.getElementById('save-arrange-five-items-btn').addEventListener('click', saveArrangeFiveItemRates);

    // 开奖设置按钮
    document.getElementById('save-double-ball-draw-settings-btn').addEventListener('click', saveDoubleBallDrawSettings);
    document.getElementById('save-super-lotto-draw-settings-btn').addEventListener('click', saveSuperLottoDrawSettings);
    
    // 账户操作按钮
    document.getElementById('deposit-btn').addEventListener('click', function() {
        const amount = parseInt(document.getElementById('deposit-amount').value);
        if (amount > 0) {
            manageAccount('deposit', amount);
        }
    });

    document.getElementById('withdraw-btn').addEventListener('click', function() {
        const amount = parseInt(document.getElementById('withdraw-amount').value);
        if (amount > 0) {
            manageAccount('withdraw', amount);
        }
    });

    // 修复输入框焦点问题
    const depositInput = document.getElementById('deposit-amount');
    const withdrawInput = document.getElementById('withdraw-amount');

    // 防止输入框失去焦点
    [depositInput, withdrawInput].forEach(input => {
        if (input) {
            input.addEventListener('mousedown', function(e) {
                e.stopPropagation();
            });

            input.addEventListener('click', function(e) {
                e.stopPropagation();
                this.focus();
            });

            input.addEventListener('focus', function(e) {
                e.stopPropagation();
            });

            // 添加回车键支持
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    const amount = parseInt(this.value);
                    if (amount > 0) {
                        if (this.id === 'deposit-amount') {
                            manageAccount('deposit', amount);
                        } else if (this.id === 'withdraw-amount') {
                            manageAccount('withdraw', amount);
                        }
                    }
                }
            });
        }
    });
    
    // 添加刷新账户信息按钮事件
    const accountCards = document.querySelectorAll('.account-card');
    accountCards.forEach(card => {
        card.addEventListener('click', function() {
            refreshAccountInfo();
        });
    });
    
    // 设置专用的验证消息监听器
    setupVerificationMessageListener();
    
    // 主NUI消息监听器
    window.addEventListener('message', function(event) {
        const data = event.data;

        // 添加通用调试日志
        if (data && data.action) {
            console.log("NUI收到消息:", data.action, data);
        }

        // 首先单独处理验证结果消息，因为这是一个优先级较高的响应
        if (data && data.action === 'verifyPlayerIdResult') {
            console.log("主消息监听器收到验证玩家ID结果消息:", data);
            // 立即处理验证结果
            handleVerifyPlayerIdResult(data);
            return; // 处理完毕，不再继续
        }
        
        // 处理其他类型的消息
        if (data && data.action === 'openAdminSystem') {
            adminData = data.data;
            currentTab = data.currentTab || 'sales';
            openAdminSystem();

            // 在打开系统时请求职业等级列表
            requestJobGrades();
        } else if (data && data.action === 'updateAccountBalance') {
            // 检查操作是否成功，data.success为true或未定义时表示成功
            const isSuccess = data.success !== false;
            
            // 无论操作成功与否，只要有账户数据就更新UI
            if (data.account) {
                // 强制转换数据类型为数字
                if (typeof data.account.balance === 'string') {
                    data.account.balance = parseInt(data.account.balance) || 0;
                }
                if (typeof data.account.total_income === 'string') {
                    data.account.total_income = parseInt(data.account.total_income) || 0;
                }
                if (typeof data.account.total_payout === 'string') {
                    data.account.total_payout = parseInt(data.account.total_payout) || 0;
                }
                
                // 更新全局数据中的账户信息
                if (adminData) {
                    adminData.account = data.account;
                    
                    // 如果服务器发送了玩家现金数据，也更新它
                    if (data.playerCash !== undefined) {
                        if (!adminData.playerData) {
                            adminData.playerData = {};
                        }
                        adminData.playerData.money = data.playerCash;
                    }
                    
                    // 如果服务器发送了交易明细数据，也更新它
                    if (data.transactions) {
                        // 移除所有临时交易记录
                        if (adminData.transactions) {
                            adminData.transactions = adminData.transactions.filter(t => !t.is_temporary);
                        }
                        
                        // 添加服务器返回的交易记录
                        adminData.transactions = data.transactions;
                        
                        // 重新渲染交易明细
                        renderTransactions(data.transactions);
                    }
                }
                
                // 如果操作失败，强制更新UI以显示正确的数据
                if (!isSuccess) {
                    updateAccountInfo(data.account);
                }
            }
        } else if (data && data.action === 'receiveAdminData') {
            adminData = data.data;
            openAdminSystem();
            renderData();
            
            // 在接收管理系统数据后请求职业等级列表
            requestJobGrades();
            
            // 手续费分成设置已移除
        } else if (data && data.action === 'updateEmployeeList') {
            // 更新员工列表
            if (adminData) {
                adminData.employees = data.employees;
                
                // 确保在渲染员工列表前已获取职业等级数据
                if (!adminData.jobGrades) {
                    requestJobGrades();
                    // 短暂延迟后渲染员工列表，给职业等级数据请求一些时间
                    setTimeout(() => {
                        renderEmployees(data.employees);
                    }, 300);
                } else {
                    renderEmployees(data.employees);
                }
            }
        } else if (data && data.action === 'receiveLotteryConfig') {
            // 收到彩票配置数据
            console.log("收到彩票配置数据:", data.config);
            console.log("双色球配置:", data.config.double_ball);
            console.log("大乐透配置:", data.config.super_lotto);
            console.log("刮刮乐配置:", data.config.scratch_cards);

            // 更新UI
            updateLotteryConfigUI(data.config);

            // 显示提示
            showNotification('彩票配置已加载', 'success');
        } else if (data && data.action === 'saveLotteryConfigResult') {
            // 处理保存结果
            if (data.success) {
                showNotification('彩票配置已永久保存', 'success');

                // 不要立即重新渲染UI，避免覆盖用户的修改
                // 只在用户刷新页面或重新打开时才显示最新配置
                console.log('配置保存成功，但不重新渲染UI以保持用户修改');
            } else {
                showNotification(data.message || '保存失败，请重试', 'error');
            }
        } else if (data && data.action === 'saveScratchConfigResult') {
            // 处理刮刮乐配置保存结果
            if (data.success) {
                showNotification('刮刮乐配置已永久保存', 'success');

                // 如果服务器返回了最新配置，更新UI但不切换标签页
                if (data.config) {
                    updateLotteryConfigUIWithoutTabSwitch(data.config);
                }
            } else {
                showNotification(data.message || '保存失败，请重试', 'error');
            }
        } else if (data && data.action === 'saveScratchRatesResult') {
            // 处理刮刮乐权重配置保存结果
            if (data.success) {
                showNotification('刮刮乐权重配置已永久保存', 'success');

                // 如果服务器返回了最新配置，更新UI但不切换标签页
                if (data.config) {
                    updateLotteryConfigUIWithoutTabSwitch(data.config);
                }
            } else {
                showNotification(data.message || '保存失败，请重试', 'error');
            }
        } else if (data && data.action === 'refreshEmployeeUI') {
            // 收到强制刷新员工界面的事件
            console.log("收到强制刷新员工界面事件");
            
            // 请求最新的员工数据和职业等级数据
            requestEmployeeList();
            requestJobGrades();
            
            // 显示刷新提示
            showNotification('员工数据已更新', 'info');
        } else if (data && data.action === 'refreshAdminData') {
            // 收到刷新管理系统数据的事件
            console.log("收到刷新管理系统数据事件");
            
            // 请求最新的管理系统数据
            requestAdminData();
            
            // 显示刷新提示
            showNotification('数据已更新', 'success');
        } else if (data && data.action === 'receiveEmployeeLogs') {
            // 渲染员工日志
            renderEmployeeLogs(data.employeeId, data.logs);
        } else if (data && data.action === 'refreshEmployeeSalary') {
            // 收到更新员工薪资的事件
            console.log("收到refreshEmployeeSalary事件:", data);
            
            // 立即更新特定员工的薪资显示
            refreshEmployeeSalary(data.employeeId, data.newSalary, data.employeeName);
        } else if (data && data.action === 'showConfigPage') {
            // 显示彩票配置页面
            showLotteryConfigPage();
        } else if (data && data.action === 'receiveJobGrades') {
            // 更新职业等级选项
            if (adminData) {
                adminData.jobGrades = data.jobGrades;
                renderJobGradeOptions(data.jobGrades);
                
                // 如果有员工数据，重新渲染员工列表以使用新的职业等级名称
                if (adminData.employees) {
                    renderEmployees(adminData.employees);
                }
            }
        } else if (data && data.action === 'directEmployeeUpdate') {
            // 直接更新特定员工的信息
            console.log("收到直接员工更新:", data);
            handleDirectEmployeeUpdate(data.updateType, data.employee);
        } else if (data && data.action === 'updateCompleteEmployeeData') {
            // 更新完整的员工数据
            console.log("收到完整员工数据更新:", data.employees.length + "条记录");
            
            // 更新全局数据
            if (adminData) {
                adminData.employees = data.employees;
            }
            
            // 只有当员工管理标签页是活动的时才更新界面
            if (currentTab === 'employees') {
                populateEmployeeTable(data.employees);
            }
        // 手续费分成设置事件处理已移除
        } else if (data && data.action === 'refreshEmployeeLevel') {
            // 收到更新员工等级的事件
            console.log("收到refreshEmployeeLevel事件:", data);
            
            // 立即更新特定员工的等级显示
            refreshEmployeeLevel(data.employeeId, data.newLevel, data.employeeName);
        } else if (data && data.action === 'updateEmployeeCommissionSettings') {
            // 更新手续费分成设置UI
            updateEmployeeCommissionSettingsUI(data.settings);
            
            // 显示成功提示
            if (data.success) {
                showNotification("手续费分成设置已保存", "success");
            }
        }
    });
    
    // 初始化时请求数据
    requestAdminData();
    
    // 刷新员工状态按钮
    const refreshEmployeeBtn = document.getElementById('refresh-employee-btn');
    if (refreshEmployeeBtn) {
        refreshEmployeeBtn.addEventListener('click', function() {
            requestEmployeeList();
            showNotification('正在刷新员工状态...', 'info');
        });
    }
    
    // 手续费分成设置保存按钮已移除
});

// 请求管理系统数据
function requestAdminData() {
    // 如果在iframe中，向父窗口发送消息
    if (window.parent !== window) {
        window.parent.postMessage({
            action: 'requestAdminData'
        }, '*');
    } else {
        // 直接向游戏发送请求
        fetch(`https://${GetParentResourceName()}/getAdminData`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .catch(error => {
            // 请求失败，忽略错误
        });
    }
}

// 打开管理系统
function openAdminSystem() {
    document.getElementById('admin-system').classList.remove('hidden');

    // 切换到销售情况页面（默认页面）
    switchTab('sales');
    renderData();

    // 不再自动加载彩票配置数据，只有通过指令显示配置页面时才加载
    console.log("管理系统打开，默认显示销售情况页面");
}

// 显示配置说明
function showConfigNotice() {
    // 创建配置说明提示
    const notice = document.createElement('div');
    notice.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        z-index: 10000;
        max-width: 350px;
        font-size: 14px;
        line-height: 1.5;
        border-left: 4px solid #ffd700;
    `;

    notice.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 8px; display: flex; align-items: center;">
            <span style="margin-right: 8px;">⚠️</span>
            配置说明
        </div>
        <div style="margin-bottom: 8px;">
            • 当前显示的是 config.lua 中的实际配置
        </div>
        <div style="margin-bottom: 8px;">
            • 修改配置将永久保存到 config_override.json 文件
        </div>
        <div style="margin-bottom: 8px;">
            • 重启服务器后修改的配置仍然有效
        </div>
        <div style="margin-bottom: 12px; color: #90EE90;">
            ✅ 配置覆盖系统确保安全的永久保存
        </div>
        <button onclick="this.parentElement.remove()" style="
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        ">知道了</button>
    `;

    document.body.appendChild(notice);

    // 5秒后自动消失
    setTimeout(() => {
        if (notice.parentElement) {
            notice.remove();
        }
    }, 8000);
}

// 注意：不再需要加载默认数据的函数，因为现在直接从配置文件获取实际配置

// 关闭管理系统
function closeAdminSystem() {
    document.getElementById('admin-system').classList.add('hidden');
    
    // 如果在iframe中，向父窗口发送消息
    if (window.parent !== window) {
        window.parent.postMessage({
            action: 'closeAdminSystem'
        }, '*');
    } else {
        // 直接向游戏发送请求
        fetch(`https://${GetParentResourceName()}/closeAdminSystem`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({})
        });
    }
}

// 切换标签页
function switchTab(tab) {
    // 检查是否有权限访问特定标签页
    if ((tab === 'account' || tab === 'employees') && adminData && adminData.playerData) {
        // 检查玩家职业等级是否满足要求
        const playerJobGrade = adminData.playerData.job ? adminData.playerData.job.grade : -1;
        const requiredGrade = adminData.config && adminData.config.permissions ? 
                             adminData.config.permissions.adminAccess : 3; // 默认为3级
        
        if (playerJobGrade < requiredGrade) {
            showNotification("权限不足", `您的职业等级不足，无法访问${tab === 'account' ? '账户管理' : '员工管理'}`, "error");
            return; // 阻止切换标签页
        }
    }
    
    // 检查彩票配置页面的访问权限 - 暂时禁用权限检查进行测试
    if (tab === 'lottery-config') {
        console.log("切换到彩票配置页面");
        // 暂时禁用权限检查
        // if (adminData && adminData.playerData) {
        //     const playerJobGrade = adminData.playerData.job ? adminData.playerData.job.grade : -1;
        //     const requiredGrade = adminData.config && adminData.config.permissions ?
        //                          adminData.config.permissions.adminAccess : 3;
        //
        //     if (playerJobGrade < requiredGrade) {
        //         showNotification("⚠️ 权限不足 ⚠️", `访问彩票配置页面需要职业等级≥${requiredGrade}级，您当前为${playerJobGrade}级`, "error");
        //         return;
        //     }
        // }

        // 自动加载彩票配置数据
        getLotteryConfig();
    }
    
    currentTab = tab;
    
    // 更新导航按钮状态
    const navButtons = document.querySelectorAll('.admin-nav .nav-btn');
    navButtons.forEach(button => {
        if (button.dataset.tab === tab) {
            button.classList.add('active');
        } else {
            button.classList.remove('active');
        }
    });
    
    // 更新标签页显示
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => {
        if (content.id === tab + '-tab') {
            content.classList.add('active');
        } else {
            content.classList.remove('active');
        }
    });
    
    // 根据标签页执行特定操作
    if (tab === 'sales') {
        // 销售情况标签页
        if (!salesChart) {
            initSalesChart();
        } else {
            updateSalesChart();
        }
    } else if (tab === 'employees') {
        // 员工管理标签页
        requestEmployeeList();
        requestJobGrades();
    }
}

// 设置图表过滤器
function setChartFilter(filter) {
    currentChartFilter = filter;
    
    // 更新过滤器按钮
    const chartFilters = document.querySelectorAll('.chart-filters .chart-filter');
    chartFilters.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.filter === filter) {
            btn.classList.add('active');
        }
    });
    
    // 更新图表
    updateSalesChart();
}

// 设置记录过滤器
function setRecordFilter(filter, tabId) {
    currentFilter = filter;
    
    // 更新过滤器按钮
    const recordFilters = document.querySelectorAll(`#${tabId} .record-filter .filter-btn`);
    recordFilters.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.filter === filter) {
            btn.classList.add('active');
        }
    });
    
    // 更新表格
    if (tabId === 'winning-tab') {
        renderWinningRecords();
    } else if (tabId === 'claimed-tab') {
        renderClaimRecords();
    } else if (tabId === 'unclaimed-tab') {
        renderUnclaimedRecords();
    }
}

// 设置交易记录过滤器
function setTransactionFilter(filter) {
    if (!filter) return;
    
    // 更新过滤器按钮状态
    const filterButtons = document.querySelectorAll('.transaction-history .filter-btn');
    filterButtons.forEach(btn => {
        if (btn.dataset.filter === filter) {
            btn.classList.add('active');
        } else {
            btn.classList.remove('active');
        }
    });
    
    // 过滤交易记录
    const rows = document.querySelectorAll('#transactions-table-body .transaction-row');
    rows.forEach(row => {
        if (filter === 'all' || row.dataset.type === filter) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// 账户操作
function manageAccount(action, amount) {
    // 直接预更新UI，不等待服务器响应
    if (action === 'deposit' && adminData && adminData.account) {
        // 获取玩家当前现金
        const playerCash = adminData.playerData?.money || 0;
        
        // 只有当玩家现金足够时才预先更新UI
        if (playerCash >= amount) {
            // 存款操作，增加余额
            adminData.account.balance = parseInt(adminData.account.balance || 0) + parseInt(amount);
            adminData.account.total_income = parseInt(adminData.account.total_income || 0) + parseInt(amount);
            
            // 添加临时交易记录
            addTemporaryTransaction('deposit', amount, '彩票店存款');
        }
    } else if (action === 'withdraw' && adminData && adminData.account) {
        // 取款操作，检查账户余额是否足够
        const currentBalance = parseInt(adminData.account.balance || 0);
        
        if (currentBalance >= amount) {
            // 减少余额
            adminData.account.balance = currentBalance - parseInt(amount);
            adminData.account.total_expense = parseInt(adminData.account.total_expense || 0) + parseInt(amount);
            
            // 添加临时交易记录
            addTemporaryTransaction('withdraw', amount, '彩票店取款');
        }
    }
    
    // 更新账户信息显示
    updateAccountInfo(adminData.account);
    
    // 发送请求到服务器
    fetch(`https://${GetParentResourceName()}/manageAccount`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action, amount })
    });
}

// 添加临时交易记录
function addTemporaryTransaction(type, amount, description) {
    if (!adminData) {
        adminData = { transactions: [] };
    }
    
    if (!adminData.transactions) {
        adminData.transactions = [];
    }
    
    // 创建临时交易记录
    const tempTransaction = {
        id: 'temp_' + Date.now(),
        transaction_type: type,
        amount: amount,
        description: description,
        player_name: '当前操作',
        timestamp: new Date().toISOString(),
        is_temporary: true // 标记为临时记录
    };
    
    // 添加到交易记录数组的开头
    adminData.transactions.unshift(tempTransaction);
    
    // 重新渲染交易明细
    renderTransactions(adminData.transactions);
}

// 渲染数据
function renderData() {
    if (!adminData) {
        return;
    }
    
    // 渲染销售数据
    renderSalesData();
    
    // 渲染中奖记录
    renderWinningRecords();
    
    // 渲染兑奖记录
    renderClaimRecords();
    
    // 渲染未兑奖记录
    renderUnclaimedRecords();
    
    // 渲染账户信息
    updateAccountInfo(adminData.account);
    
    // 渲染交易明细
    renderTransactions(adminData.transactions);
    
    // 渲染员工列表
    if (adminData.employees) {
        renderEmployees(adminData.employees);
    }
    
    // 渲染职业等级选项
    if (adminData.jobGrades) {
        renderJobGradeOptions(adminData.jobGrades);
    }
}

// 渲染销售数据
function renderSalesData() {
    if (!adminData) {
        // 显示默认值
        document.getElementById('total-sales-count').textContent = '0';
        document.getElementById('total-sales-amount').textContent = '¥0';
        document.getElementById('scratch-sales-count').textContent = '0';
        document.getElementById('lottery-sales-count').textContent = '0';
        return;
    }
    
    if (!adminData.sales || !adminData.sales.total) {
        // 显示默认值
        document.getElementById('total-sales-count').textContent = '0';
        document.getElementById('total-sales-amount').textContent = '¥0';
        document.getElementById('scratch-sales-count').textContent = '0';
        document.getElementById('lottery-sales-count').textContent = '0';
        return;
    }
    
    const sales = adminData.sales;
    
    // 确保数据有效性
    const totalSold = sales.total.total_sold || 0;
    const totalRevenue = sales.total.total_revenue || 0;
    const scratchSold = sales.total.total_scratch_sold || 0;
    const lotterySold = sales.total.total_lottery_sold || 0;
    
    // 更新销售摘要
    document.getElementById('total-sales-count').textContent = totalSold.toLocaleString();
    document.getElementById('total-sales-amount').textContent = `¥${totalRevenue.toLocaleString()}`;
    document.getElementById('scratch-sales-count').textContent = scratchSold.toLocaleString();
    document.getElementById('lottery-sales-count').textContent = lotterySold.toLocaleString();
    
    // 初始化销售图表
    initSalesChart();
    
    // 渲染销售表格
    const tableBody = document.getElementById('sales-table-body');
    tableBody.innerHTML = '';
    
    if (!sales.daily || !Array.isArray(sales.daily) || sales.daily.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="7" style="text-align:center;">暂无销售数据</td></tr>';
        return;
    }
    
    sales.daily.forEach(day => {
        const row = document.createElement('tr');
        
        // 格式化日期显示
        const formattedDate = formatDateForDisplay(day.date);
        
        row.innerHTML = `
            <td>${formattedDate}</td>
            <td>${(day.scratch_sold || 0).toLocaleString()}</td>
            <td>¥${(day.scratch_revenue || 0).toLocaleString()}</td>
            <td>${(day.lottery_sold || 0).toLocaleString()}</td>
            <td>¥${(day.lottery_revenue || 0).toLocaleString()}</td>
            <td>${(day.total_sold || 0).toLocaleString()}</td>
            <td>¥${(day.total_revenue || 0).toLocaleString()}</td>
        `;
        tableBody.appendChild(row);
    });
}

// 初始化销售图表
function initSalesChart() {
    if (!adminData || !adminData.sales || !adminData.sales.daily || !Array.isArray(adminData.sales.daily) || adminData.sales.daily.length === 0) {
        return;
    }
    
    // 获取图表容器
    const chartContainer = document.getElementById('sales-chart');
    if (!chartContainer) {
        return;
    }
    
    // 清空图表容器
    chartContainer.innerHTML = '';
    
    const sales = adminData.sales.daily;
    
    // 创建图表容器样式
    chartContainer.style.position = 'relative';
    chartContainer.style.height = '300px';
    chartContainer.style.width = '100%';
    chartContainer.style.marginTop = '20px';
    chartContainer.style.marginBottom = '40px';
    chartContainer.style.borderBottom = '1px solid #ddd';
    
    // 找出最大值用于缩放
    let maxRevenue = 0;
    sales.forEach(day => {
        const total = (day.total_revenue || 0);
        if (total > maxRevenue) maxRevenue = total;
    });
    
    // 如果最大值为0，设置为1避免除以0错误
    if (maxRevenue === 0) maxRevenue = 1;
    
    // 计算图表尺寸
    const chartWidth = chartContainer.clientWidth - 40; // 左右各留20px边距
    const chartHeight = 250; // 图表高度
    const barCount = Math.min(sales.length, 30); // 最多显示30天
    const barWidth = Math.max(15, Math.min(40, (chartWidth / barCount) - 10)); // 每个柱子的宽度，最小15px，最大40px
    const barSpacing = 10; // 柱子之间的间距
    
    // 创建图表容器
    const chartInner = document.createElement('div');
    chartInner.style.position = 'relative';
    chartInner.style.height = chartHeight + 'px';
    chartInner.style.marginLeft = '20px';
    chartInner.style.marginRight = '20px';
    
    // 添加Y轴标签
    const yAxisLabels = [0, maxRevenue / 2, maxRevenue];
    yAxisLabels.forEach((value, index) => {
        const label = document.createElement('div');
        label.style.position = 'absolute';
        label.style.right = '100%';
        label.style.top = (100 - (index * 50)) + '%';
        label.style.transform = 'translateY(-50%)';
        label.style.marginRight = '5px';
        label.style.fontSize = '12px';
        label.style.color = '#666';
        label.textContent = '¥' + Math.round(value).toLocaleString();
        chartInner.appendChild(label);
    });
    
    // 添加水平参考线
    yAxisLabels.forEach((value, index) => {
        const line = document.createElement('div');
        line.style.position = 'absolute';
        line.style.left = '0';
        line.style.right = '0';
        line.style.top = (100 - (index * 50)) + '%';
        line.style.borderTop = '1px dashed #ddd';
        chartInner.appendChild(line);
    });
    
    // 添加柱状图
    sales.slice(0, barCount).forEach((day, index) => {
        // 创建柱子容器
        const barContainer = document.createElement('div');
        barContainer.style.position = 'absolute';
        barContainer.style.bottom = '0';
        barContainer.style.left = (index * (barWidth + barSpacing)) + 'px';
        barContainer.style.width = barWidth + 'px';
        barContainer.style.height = chartHeight + 'px';
        
        // 计算高度百分比
        const scratchRevenue = day.scratch_revenue || 0;
        const lotteryRevenue = day.lottery_revenue || 0;
        const scratchHeight = (scratchRevenue / maxRevenue) * chartHeight;
        const lotteryHeight = (lotteryRevenue / maxRevenue) * chartHeight;
        
        // 创建悬停提示框
        const tooltip = document.createElement('div');
        tooltip.style.position = 'absolute';
        tooltip.style.display = 'none';
        tooltip.style.backgroundColor = 'rgba(0,0,0,0.8)';
        tooltip.style.color = '#fff';
        tooltip.style.padding = '8px 12px';
        tooltip.style.borderRadius = '4px';
        tooltip.style.fontSize = '12px';
        tooltip.style.zIndex = '1000';
        tooltip.style.whiteSpace = 'nowrap';
        tooltip.style.pointerEvents = 'none'; // 不影响鼠标事件
        tooltip.style.boxShadow = '0 2px 8px rgba(0,0,0,0.3)';
        tooltip.style.transition = 'opacity 0.2s';
        tooltip.setAttribute('data-chart-tooltip', 'true'); // 添加标识属性
        
        // 格式化日期和金额
        const dateStr = formatDateForDisplay(day.date);
        const formattedScratchRevenue = scratchRevenue.toLocaleString();
        const formattedLotteryRevenue = lotteryRevenue.toLocaleString();
        const totalRevenue = day.total_revenue || (scratchRevenue + lotteryRevenue);
        const formattedTotalRevenue = totalRevenue.toLocaleString();
        
        // 设置提示内容
        tooltip.innerHTML = `
            <div style="margin-bottom:5px;font-weight:bold;">${dateStr}</div>
            <div style="display:flex;justify-content:space-between;margin-bottom:3px;">
                <span style="color:#3498db;margin-right:10px;">刮刮乐:</span>
                <span>¥${formattedScratchRevenue}</span>
            </div>
            <div style="display:flex;justify-content:space-between;margin-bottom:3px;">
                <span style="color:#2ecc71;margin-right:10px;">彩票:</span>
                <span>¥${formattedLotteryRevenue}</span>
            </div>
            <div style="display:flex;justify-content:space-between;border-top:1px solid rgba(255,255,255,0.2);padding-top:3px;margin-top:3px;">
                <span style="margin-right:10px;">总计:</span>
                <span>¥${formattedTotalRevenue}</span>
            </div>
        `;
        
        document.body.appendChild(tooltip);
        
        // 创建鼠标悬停区域（覆盖整个柱子）
        const hoverArea = document.createElement('div');
        hoverArea.style.position = 'absolute';
        hoverArea.style.bottom = '0';
        hoverArea.style.left = '0';
        hoverArea.style.width = '100%';
        hoverArea.style.height = '100%';
        hoverArea.style.cursor = 'pointer';
        hoverArea.style.zIndex = '10';
        
        // 添加鼠标事件
        hoverArea.addEventListener('mousemove', function(e) {
            // 显示提示框
            tooltip.style.display = 'block';
            
            // 计算提示框位置
            const rect = hoverArea.getBoundingClientRect();
            const tooltipRect = tooltip.getBoundingClientRect();
            
            // 提示框显示在柱子上方
            let left = rect.left + rect.width / 2 - tooltipRect.width / 2;
            let top = rect.top - tooltipRect.height - 10;
            
            // 确保提示框不会超出屏幕
            if (left < 10) left = 10;
            if (left + tooltipRect.width > window.innerWidth - 10) 
                left = window.innerWidth - tooltipRect.width - 10;
            if (top < 10) top = rect.bottom + 10;
            
            // 设置提示框位置
            tooltip.style.left = left + 'px';
            tooltip.style.top = top + 'px';
        });
        
        hoverArea.addEventListener('mouseout', function() {
            // 隐藏提示框
            tooltip.style.display = 'none';
        });
        
        // 创建刮刮乐柱子
        if ((currentChartFilter === 'all' || currentChartFilter === 'scratch') && scratchRevenue > 0) {
            const scratchBar = document.createElement('div');
            scratchBar.style.position = 'absolute';
            scratchBar.style.bottom = '0';
            scratchBar.style.left = '0';
            scratchBar.style.width = (barWidth / 2 - 1) + 'px';
            scratchBar.style.height = scratchHeight + 'px';
            scratchBar.style.backgroundColor = '#3498db';
            scratchBar.style.transition = 'height 0.3s ease, opacity 0.2s';
            barContainer.appendChild(scratchBar);
            
            // 添加悬停效果
            hoverArea.addEventListener('mouseover', function() {
                scratchBar.style.opacity = '0.8';
                scratchBar.style.boxShadow = '0 0 8px rgba(52, 152, 219, 0.6)';
            });
            
            hoverArea.addEventListener('mouseout', function() {
                scratchBar.style.opacity = '1';
                scratchBar.style.boxShadow = 'none';
            });
        }
        
        // 创建彩票柱子
        if ((currentChartFilter === 'all' || currentChartFilter === 'lottery') && lotteryRevenue > 0) {
            const lotteryBar = document.createElement('div');
            lotteryBar.style.position = 'absolute';
            lotteryBar.style.bottom = '0';
            lotteryBar.style.right = '0';
            lotteryBar.style.width = (barWidth / 2 - 1) + 'px';
            lotteryBar.style.height = lotteryHeight + 'px';
            lotteryBar.style.backgroundColor = '#2ecc71';
            lotteryBar.style.transition = 'height 0.3s ease, opacity 0.2s';
            barContainer.appendChild(lotteryBar);
            
            // 添加悬停效果
            hoverArea.addEventListener('mouseover', function() {
                lotteryBar.style.opacity = '0.8';
                lotteryBar.style.boxShadow = '0 0 8px rgba(46, 204, 113, 0.6)';
            });
            
            hoverArea.addEventListener('mouseout', function() {
                lotteryBar.style.opacity = '1';
                lotteryBar.style.boxShadow = 'none';
            });
        }
        
        barContainer.appendChild(hoverArea);
        
        // 添加日期标签
        const dateLabel = document.createElement('div');
        dateLabel.style.position = 'absolute';
        dateLabel.style.bottom = '-25px';
        dateLabel.style.left = '0';
        dateLabel.style.width = barWidth + 'px';
        dateLabel.style.textAlign = 'center';
        dateLabel.style.fontSize = '11px';
        dateLabel.style.color = '#666';
        dateLabel.style.whiteSpace = 'nowrap';
        
        // 安全处理日期，只显示月和日
        let dayText = '';
        if (day.date) {
            dayText = formatDateForDisplay(day.date, true); // 使用短格式，只显示月和日
        } else {
            dayText = String(index + 1);
        }
        
        dateLabel.textContent = dayText;
        barContainer.appendChild(dateLabel);
        
        chartInner.appendChild(barContainer);
    });
    
    // 添加图例
    const legend = document.createElement('div');
    legend.style.position = 'absolute';
    legend.style.top = '10px';
    legend.style.right = '20px';
    legend.style.display = 'flex';
    legend.style.alignItems = 'center';
    legend.style.gap = '15px';
    
    if (currentChartFilter === 'all' || currentChartFilter === 'scratch') {
        const scratchLegend = document.createElement('div');
        scratchLegend.style.display = 'flex';
        scratchLegend.style.alignItems = 'center';
        
        const scratchColor = document.createElement('div');
        scratchColor.style.width = '12px';
        scratchColor.style.height = '12px';
        scratchColor.style.backgroundColor = '#3498db';
        scratchColor.style.marginRight = '5px';
        
        const scratchText = document.createElement('span');
        scratchText.textContent = '刮刮乐';
        scratchText.style.fontSize = '12px';
        
        scratchLegend.appendChild(scratchColor);
        scratchLegend.appendChild(scratchText);
        legend.appendChild(scratchLegend);
    }
    
    if (currentChartFilter === 'all' || currentChartFilter === 'lottery') {
        const lotteryLegend = document.createElement('div');
        lotteryLegend.style.display = 'flex';
        lotteryLegend.style.alignItems = 'center';
        
        const lotteryColor = document.createElement('div');
        lotteryColor.style.width = '12px';
        lotteryColor.style.height = '12px';
        lotteryColor.style.backgroundColor = '#2ecc71';
        lotteryColor.style.marginRight = '5px';
        
        const lotteryText = document.createElement('span');
        lotteryText.textContent = '彩票';
        lotteryText.style.fontSize = '12px';
        
        lotteryLegend.appendChild(lotteryColor);
        lotteryLegend.appendChild(lotteryText);
        legend.appendChild(lotteryLegend);
    }
    
    chartContainer.appendChild(chartInner);
    chartContainer.appendChild(legend);
}

// 重写清理函数，确保移除所有提示框
function cleanupChartTooltips() {
    // 移除所有由图表创建的提示框
    const tooltips = document.querySelectorAll('div[data-chart-tooltip]');
    tooltips.forEach(tooltip => {
        document.body.removeChild(tooltip);
    });
}

// 修改updateSalesChart函数
function updateSalesChart() {
    // 清理旧的提示框
    cleanupChartTooltips();
    // 重新初始化图表
    initSalesChart();
}

// 渲染中奖记录
function renderWinningRecords() {
    if (!adminData || !adminData.winningRecords) return;
    
    const tableBody = document.getElementById('winning-table-body');
    if (!tableBody) {
        return;
    }
    
    tableBody.innerHTML = '';
    
    let records = [];
    
    try {
        if (currentFilter === 'all' || currentFilter === 'scratch') {
            if (Array.isArray(adminData.winningRecords.scratch)) {
                records = records.concat(adminData.winningRecords.scratch);
            }
        }
        
        if (currentFilter === 'all' || currentFilter === 'lottery') {
            if (Array.isArray(adminData.winningRecords.lottery)) {
                records = records.concat(adminData.winningRecords.lottery);
            }
        }
        
        // 按时间排序
        records.sort((a, b) => {
            const timeA = a.scratch_time || a.draw_date || '';
            const timeB = b.scratch_time || b.draw_date || '';
            return new Date(timeB) - new Date(timeA);
        });
        
        records.forEach(record => {
            const row = document.createElement('tr');
            const type = record.card_type ? `刮刮乐(${record.card_name || ''})` : `彩票(${record.lottery_type === 'double_ball' ? '双色球' : '大乐透'})`;
            const winTime = record.scratch_time || record.draw_date || '-';
            const claimStatus = record.is_claimed ? '已兑奖' : '未兑奖';
            const prizeAmount = record.prize_amount || 0;
            
            row.innerHTML = `
                <td>${record.player_name || ''}</td>
                <td>${type}</td>
                <td>¥${typeof prizeAmount.toLocaleString === 'function' ? prizeAmount.toLocaleString() : prizeAmount}</td>
                <td>${record.purchase_time || '-'}</td>
                <td>${winTime}</td>
                <td>${claimStatus}</td>
            `;
            tableBody.appendChild(row);
        });
    } catch (e) {
        tableBody.innerHTML = '<tr><td colspan="6" style="text-align:center;">渲染数据时出错</td></tr>';
    }
}

// 渲染兑奖记录
function renderClaimRecords() {
    if (!adminData || !adminData.claimRecords) return;
    
    const tableBody = document.getElementById('claimed-table-body');
    if (!tableBody) {
        return;
    }
    
    tableBody.innerHTML = '';
    
    let records = [];
    
    try {
        if (currentFilter === 'all' || currentFilter === 'scratch') {
            if (Array.isArray(adminData.claimRecords.scratch)) {
                records = records.concat(adminData.claimRecords.scratch);
            }
        }
        
        if (currentFilter === 'all' || currentFilter === 'lottery') {
            if (Array.isArray(adminData.claimRecords.lottery)) {
                records = records.concat(adminData.claimRecords.lottery);
            }
        }
        
        // 按兑奖时间排序
        records.sort((a, b) => {
            return new Date(b.claimed_time || 0) - new Date(a.claimed_time || 0);
        });
        
        records.forEach(record => {
            const row = document.createElement('tr');
            const type = record.card_type ? `刮刮乐(${record.card_name || ''})` : `彩票(${record.lottery_type === 'double_ball' ? '双色球' : '大乐透'})`;
            const winTime = record.scratch_time || record.draw_date || '-';
            const prizeAmount = record.prize_amount || 0;
            
            row.innerHTML = `
                <td>${record.player_name || ''}</td>
                <td>${type}</td>
                <td>¥${typeof prizeAmount.toLocaleString === 'function' ? prizeAmount.toLocaleString() : prizeAmount}</td>
                <td>${record.purchase_time || '-'}</td>
                <td>${winTime}</td>
                <td>${record.claimed_time || '-'}</td>
            `;
            tableBody.appendChild(row);
        });
    } catch (e) {
        tableBody.innerHTML = '<tr><td colspan="6" style="text-align:center;">渲染数据时出错</td></tr>';
    }
}

// 渲染未兑奖记录
function renderUnclaimedRecords() {
    if (!adminData || !adminData.unclaimedRecords) return;
    
    const tableBody = document.getElementById('unclaimed-table-body');
    if (!tableBody) {
        return;
    }
    
    tableBody.innerHTML = '';
    
    let records = [];
    
    try {
        if (currentFilter === 'all' || currentFilter === 'scratch') {
            if (Array.isArray(adminData.unclaimedRecords.scratch)) {
                records = records.concat(adminData.unclaimedRecords.scratch);
            }
        }
        
        if (currentFilter === 'all' || currentFilter === 'lottery') {
            if (Array.isArray(adminData.unclaimedRecords.lottery)) {
                records = records.concat(adminData.unclaimedRecords.lottery);
            }
        }
        
        // 按中奖时间排序
        records.sort((a, b) => {
            const timeA = a.scratch_time || a.draw_date || '';
            const timeB = b.scratch_time || b.draw_date || '';
            return new Date(timeB) - new Date(timeA);
        });
        
        records.forEach(record => {
            const row = document.createElement('tr');
            const type = record.card_type ? `刮刮乐(${record.card_name || ''})` : `彩票(${record.lottery_type === 'double_ball' ? '双色球' : '大乐透'})`;
            const winTime = record.scratch_time || record.draw_date || '-';
            const prizeAmount = record.prize_amount || 0;
            const lotteryType = record.card_type ? 'scratch' : 'lottery';
            
            row.innerHTML = `
                <td>${record.player_name || ''}</td>
                <td>${type}</td>
                <td>¥${typeof prizeAmount.toLocaleString === 'function' ? prizeAmount.toLocaleString() : prizeAmount}</td>
                <td>${record.purchase_time || '-'}</td>
                <td>${winTime}</td>
                <td>
                    <button class="claim-btn" data-id="${record.id}" data-type="${lotteryType}">兑奖</button>
                </td>
            `;
            tableBody.appendChild(row);
        });
        
        // 添加兑奖按钮点击事件
        const claimButtons = tableBody.querySelectorAll('.claim-btn');
        claimButtons.forEach(button => {
            button.addEventListener('click', function() {
                const ticketId = this.dataset.id;
                const lotteryType = this.dataset.type;
                claimPrize(ticketId, lotteryType);
            });
        });
    } catch (e) {
        tableBody.innerHTML = '<tr><td colspan="6" style="text-align:center;">渲染数据时出错</td></tr>';
    }
}

// 处理兑奖请求
function claimPrize(ticketId, lotteryType) {
    if (!ticketId || !lotteryType) {
        showNotification('兑奖失败: 参数错误', 'error');
        return;
    }
    
    // 显示处理中提示
    showNotification('正在处理兑奖...', 'info');
    
    // 发送兑奖请求到服务器
    fetch(`https://${GetParentResourceName()}/adminClaimPrize`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            ticketId: ticketId,
            lotteryType: lotteryType
        })
    }).catch(err => {
        console.error('兑奖请求失败:', err);
        showNotification('兑奖请求失败', 'error');
    });
    
    // 立即从UI中移除该项目
    try {
        // 查找并移除未兑奖列表中的对应项目
        const row = document.querySelector(`#unclaimed-table-body tr button[data-id="${ticketId}"][data-type="${lotteryType}"]`).closest('tr');
        if (row) {
            row.style.transition = 'opacity 0.5s';
            row.style.opacity = '0';
            setTimeout(() => {
                row.remove();
                
                // 检查是否还有未兑奖项目
                const rows = document.querySelectorAll('#unclaimed-table-body tr:not(.empty-row)');
                if (rows.length === 0) {
                    // 如果没有未兑奖项目了，显示"暂无记录"提示
                    const tableBody = document.getElementById('unclaimed-table-body');
                    if (tableBody) {
                        const emptyRow = document.createElement('tr');
                        emptyRow.className = 'empty-row';
                        emptyRow.innerHTML = '<td colspan="6" class="empty-message">暂无未兑奖记录</td>';
                        tableBody.appendChild(emptyRow);
                    }
                }
                
                // 主动请求更新数据
                setTimeout(() => {
                    // 请求最新的中奖记录和兑奖记录数据
                    requestAdminData();
                }, 300);
            }, 500);
        }
    } catch (e) {
        console.error('移除兑奖项目失败:', e);
    }
}

// 更新账户信息
function updateAccountInfo(account) {
    if (!account) {
        return;
    }
    
    try {
        // 确保数值是数字类型
        const balance = parseInt(account.balance) || 0;
        const income = parseInt(account.total_income) || 0;
        const payout = parseInt(account.total_payout) || 0;
        
        // 安全获取元素并设置文本内容
        const balanceElement = document.getElementById('account-balance');
        const incomeElement = document.getElementById('account-income');
        const payoutElement = document.getElementById('account-payout');
        const updatedElement = document.getElementById('account-updated');
        
        if (balanceElement) {
            balanceElement.textContent = `¥${balance.toLocaleString()}`;
        }
        
        if (incomeElement) {
            incomeElement.textContent = `¥${income.toLocaleString()}`;
        }
        
        if (payoutElement) {
            payoutElement.textContent = `¥${payout.toLocaleString()}`;
        }
        
        if (updatedElement) {
            // 始终使用当前时间
            const now = new Date();
            const formattedDate = now.getFullYear() + '/' + 
                               padZero(now.getMonth() + 1) + '/' + 
                               padZero(now.getDate()) + ' ' +
                               padZero(now.getHours()) + ':' +
                               padZero(now.getMinutes()) + ':' +
                               padZero(now.getSeconds());
            
            updatedElement.textContent = formattedDate;
        }
    } catch (e) {
        console.error('更新账户信息时出错:', e);
    }
}

// 主动刷新账户信息
function refreshAccountInfo() {
    // 向服务器请求最新账户数据
    fetch(`https://${GetParentResourceName()}/getAccountData`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
    }).then(() => {
        // 请求已发送，等待服务器响应
    }).catch(err => {
        console.error('账户操作请求失败:', err);
    });
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '-';
    
    try {
        // 处理时间戳格式
        if (typeof dateString === 'string' && dateString.length > 10 && !isNaN(dateString)) {
            // 如果是13位时间戳（如：*************）
            const timestamp = parseInt(dateString);
            const date = new Date(timestamp);
            
            // 检查日期是否有效
            if (isNaN(date.getTime())) return dateString;
            
            // 返回YYYY-MM-DD格式
            return date.getFullYear() + '-' + 
                   padZero(date.getMonth() + 1) + '-' + 
                   padZero(date.getDate());
        }
        
        // 处理普通日期字符串
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return dateString;
        
        return date.toLocaleString('zh-CN');
    } catch (e) {
        return dateString;
    }
}

// 数字前补零
function padZero(num) {
    return num < 10 ? '0' + num : num;
}

// 安全处理日期显示
function formatDateForDisplay(dateStr, shortFormat = false) {
    if (!dateStr) return '';
    
    // 如果是数字字符串且长度大于10，可能是时间戳
    if (typeof dateStr === 'string' && dateStr.length > 10 && !isNaN(dateStr)) {
        const timestamp = parseInt(dateStr);
        const date = new Date(timestamp);
        
        // 检查日期是否有效
        if (isNaN(date.getTime())) {
            return dateStr;
        }
        
        if (shortFormat) {
            // 返回MM-DD格式
            return padZero(date.getMonth() + 1) + '-' + 
                   padZero(date.getDate());
        }
        
        // 格式化为YYYY-MM-DD
        return date.getFullYear() + '-' + 
               padZero(date.getMonth() + 1) + '-' + 
               padZero(date.getDate());
    }
    
    // 处理其他格式的日期
    try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) {
            return dateStr;
        }
        
        if (shortFormat) {
            // 返回MM-DD格式
            return padZero(date.getMonth() + 1) + '-' + 
                   padZero(date.getDate());
        }
        
        return date.getFullYear() + '-' + 
               padZero(date.getMonth() + 1) + '-' + 
               padZero(date.getDate());
    } catch (e) {
        return dateStr;
    }
}

// 显示通知
function showNotification(message, type) {
    // 防抖处理：如果相同消息在1秒内出现，则忽略
    const now = Date.now();
    if (lastNotification.message === message && 
        lastNotification.type === type && 
        now - lastNotification.time < 1000) {
        return;
    }
    
    // 更新最后通知的记录
    lastNotification = {
        message: message,
        type: type,
        time: now
    };
    
    // 清除之前的计时器
    if (notificationTimer) {
        clearTimeout(notificationTimer);
    }
    
    // 延迟发送通知，确保不会重复
    notificationTimer = setTimeout(() => {
        // 仅在UI界面时使用内置通知
        if (document.getElementById('admin-system') && 
            !document.getElementById('admin-system').classList.contains('hidden')) {
            // 使用页面内置通知
            const toast = document.createElement('div');
            toast.className = `admin-toast admin-toast-${type}`;
            toast.innerHTML = `
                <div class="admin-toast-title">${type === 'success' ? '操作成功' : '提示'}</div>
                <div class="admin-toast-message">${message}</div>
            `;
            document.body.appendChild(toast);
            
            // 动画显示
            setTimeout(() => {
                toast.classList.add('show');
            }, 10);
            
            // 3秒后移除
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        } else {
            // 使用游戏内的通知系统
            fetch(`https://${GetParentResourceName()}/showNotification`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ 
                    title: type === 'success' ? '操作成功' : '提示',
                    message: message, 
                    type: type 
                })
            });
        }
    }, 50);
}

// 物品名称映射
const itemNames = {
    // 基础物品
    "bread": "面包",
    "water": "水",
    "bandage": "绷带",
    "phone": "手机",
    "lockpick": "撬锁器",
    "diamond": "钻石",
    "gold": "金条",
    "money": "现金",
    "car_key": "车钥匙",

    // 武器类
    "weapon_pistol": "手枪",
    "weapon_knife": "小刀",
    "weapon_rifle": "步枪",
    "weapon_carbinerifle": "卡宾枪",
    "weapon_smg": "冲锋枪",

    // 工具类
    "repairkit": "修理包",
    "advancedrepairkit": "高级修理包",
    "radio": "对讲机",
    "binoculars": "望远镜",

    // 食物饮料
    "burger": "汉堡",
    "cola": "可乐",
    "coffee": "咖啡",
    "sandwich": "三明治",

    // 刮刮乐
    "scratch_card_caizuan": "5倍彩钻刮刮乐",
    "scratch_card_xixiangfeng": "喜相逢刮刮乐",
    "scratch_card_fusong": "福鼠送彩刮刮乐",
    "scratch_card_yaocai": "耀出彩刮刮乐"
};

// 获取物品显示名称
function getItemDisplayName(itemCode) {
    // 处理带数量的money格式 (例如: "money:1000")
    if (itemCode && itemCode.includes("money:")) {
        const parts = itemCode.split(":");
        if (parts.length === 2) {
            return `¥${parts[1]}`;
        }
    }

    // 使用从后端获取的物品名称映射，如果没有则使用本地映射
    let currentItemNames = itemNames; // 默认使用本地映射

    // 尝试从全局配置数据中获取物品名称映射
    if (window.lotteryConfigData && window.lotteryConfigData.itemNames) {
        currentItemNames = window.lotteryConfigData.itemNames;
    } else if (adminData && adminData.lotteryConfig && adminData.lotteryConfig.itemNames) {
        currentItemNames = adminData.lotteryConfig.itemNames;
    }

    // 返回映射的中文名称，如果没有映射则返回原始代码
    return currentItemNames[itemCode] || itemCode;
}

// 获取所有可用物品列表
function getAllAvailableItems() {
    const items = [];

    // 使用从后端获取的物品名称映射，如果没有则使用本地映射
    let currentItemNames = itemNames; // 默认使用本地映射

    // 尝试从全局配置数据中获取物品名称映射
    if (window.lotteryConfigData && window.lotteryConfigData.itemNames) {
        currentItemNames = window.lotteryConfigData.itemNames;
    } else if (adminData && adminData.lotteryConfig && adminData.lotteryConfig.itemNames) {
        currentItemNames = adminData.lotteryConfig.itemNames;
    }

    // 添加所有配置的物品
    for (const [code, name] of Object.entries(currentItemNames)) {
        if (code !== 'money') { // money单独处理
            items.push({ code: code, name: name });
        }
    }

    // 添加金钱物品（特殊处理）
    items.push({ code: 'money', name: '现金' });

    // 按名称排序
    items.sort((a, b) => a.name.localeCompare(b.name));

    return items;
}

// 切换物品下拉框显示状态
function toggleItemDropdown(dropdown) {
    const isVisible = dropdown.style.display !== 'none';

    // 先关闭所有其他下拉框并移除高层级类
    document.querySelectorAll('.item-dropdown').forEach(d => {
        if (d !== dropdown) {
            d.style.display = 'none';
            const rateItem = d.closest('.rate-item');
            if (rateItem) {
                rateItem.classList.remove('dropdown-open');
            }
        }
    });

    // 切换当前下拉框
    const newDisplay = isVisible ? 'none' : 'block';
    dropdown.style.display = newDisplay;

    // 为当前下拉框的父容器添加或移除高层级类
    const rateItem = dropdown.closest('.rate-item');
    if (rateItem) {
        if (newDisplay === 'block') {
            rateItem.classList.add('dropdown-open');

            // 智能定位：检查下拉框是否会超出视窗底部
            setTimeout(() => {
                const buttonRect = rateItem.querySelector('.item-display-button').getBoundingClientRect();
                const viewportHeight = window.innerHeight;
                const dropdownHeight = 300; // 下拉框最大高度

                const spaceBelow = viewportHeight - buttonRect.bottom;
                const spaceAbove = buttonRect.top;

                console.log('智能定位检测:', {
                    spaceBelow,
                    spaceAbove,
                    dropdownHeight,
                    viewportHeight,
                    buttonTop: buttonRect.top,
                    buttonBottom: buttonRect.bottom
                });

                // 如果下方空间不足以显示完整下拉框，且上方空间更充足，则向上展开
                if (spaceBelow < dropdownHeight && spaceAbove > spaceBelow && spaceAbove >= 200) {
                    dropdown.classList.add('dropdown-up');
                    console.log('下拉框向上展开');
                } else {
                    dropdown.classList.remove('dropdown-up');
                    console.log('下拉框向下展开');
                }
            }, 50); // 增加延迟确保DOM完全更新
        } else {
            rateItem.classList.remove('dropdown-open');
            dropdown.classList.remove('dropdown-up');
        }
    }
}

// 选择物品并更新配置
function selectItemForRate(oldKey, selectedItem, displayButton, dropdown, weightInput) {
    let newKey = selectedItem.code;

    // 如果是金钱物品，需要获取金额
    if (selectedItem.code === 'money') {
        const moneyInput = dropdown.querySelector('.money-amount-input');
        const amount = parseInt(moneyInput.value) || 1000;
        newKey = `money:${amount}`;
    }

    // 如果新key和旧key相同，不需要更新
    if (oldKey === newKey) {
        dropdown.style.display = 'none';
        const rateItem = dropdown.closest('.rate-item');
        if (rateItem) {
            rateItem.classList.remove('dropdown-open');
        }
        return;
    }

    // 更新显示按钮
    const displayName = getItemDisplayName(newKey);
    displayButton.innerHTML = `
        <span class="item-name">${displayName}</span>
        <i class="fas fa-chevron-down"></i>
    `;
    displayButton.title = `物品代码: ${newKey}`;

    // 更新权重输入框的key
    weightInput.dataset.key = newKey;
    weightInput.title = `设置 ${newKey} 的权重值，数值越大出现概率越高`;

    // 关闭下拉框并移除高层级类
    dropdown.style.display = 'none';
    const rateItem = dropdown.closest('.rate-item');
    if (rateItem) {
        rateItem.classList.remove('dropdown-open');
    }

    // 标记配置已修改
    markConfigAsModified();
}

// 标记配置为已修改状态
function markConfigAsModified() {
    // 可以在这里添加视觉提示，表示配置已修改
    console.log('配置已修改，需要保存');
}

// 刷新所有已显示的物品名称
function refreshAllItemDisplayNames() {
    // 查找所有物品显示按钮并更新其显示名称
    const itemButtons = document.querySelectorAll('.item-display-button .item-name');
    itemButtons.forEach(button => {
        const displayButton = button.parentElement;
        const weightInput = displayButton.parentElement.querySelector('.weight-input');
        if (weightInput && weightInput.dataset.key) {
            const itemKey = weightInput.dataset.key;
            const displayName = getItemDisplayName(itemKey);
            button.textContent = displayName;
            displayButton.title = `物品代码: ${itemKey}`;
        }
    });
}

// 清理无效的物品配置项
function cleanupInvalidItemRates(containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;

    const rateItems = container.querySelectorAll('.rate-item');
    let removedCount = 0;

    rateItems.forEach(rateItem => {
        const input = rateItem.querySelector('input[type="number"]');
        if (input) {
            const key = input.dataset.key;
            const value = parseFloat(input.value);

            // 移除无效的配置项（只移除真正无效的，权重为0是有效的）
            if (!key || key === 'undefined' || key.trim() === '' || isNaN(value) || value < 0) {
                rateItem.remove();
                removedCount++;
            }
        }
    });

    if (removedCount > 0) {
        console.log(`清理了 ${removedCount} 个无效的物品配置项`);
        updateRatesSummary(containerId);
    }
}

// 添加新物品按钮
function addNewItemButton(container, containerId) {
    const addButton = document.createElement('div');
    addButton.className = 'add-new-item-button';
    addButton.innerHTML = `
        <button type="button" class="btn-add-item">
            <i class="fas fa-plus"></i>
            添加新物品
        </button>
    `;

    addButton.addEventListener('click', function() {
        addNewItemRate(containerId);
    });

    container.appendChild(addButton);
}

// 添加新物品权重配置
function addNewItemRate(containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;

    // 创建新的物品配置项
    const rateItem = document.createElement('div');
    rateItem.className = 'rate-item new-item';

    // 物品选择器
    const rateKey = document.createElement('div');
    rateKey.className = 'rate-key item-selector';

    // 创建物品选择器容器
    const itemSelectorContainer = document.createElement('div');
    itemSelectorContainer.className = 'item-selector-container';

    // 创建显示当前物品的按钮
    const itemDisplayButton = document.createElement('button');
    itemDisplayButton.className = 'item-display-button';
    itemDisplayButton.type = 'button';
    itemDisplayButton.innerHTML = `
        <span class="item-name">选择物品...</span>
        <i class="fas fa-chevron-down"></i>
    `;

    // 创建下拉选择框
    const itemDropdown = document.createElement('div');
    itemDropdown.className = 'item-dropdown';
    itemDropdown.style.display = 'none';

    // 添加所有可用物品选项
    const allItems = getAllAvailableItems();
    allItems.forEach(item => {
        const option = document.createElement('div');
        option.className = 'item-option';
        option.dataset.itemCode = item.code;

        if (item.code === 'money') {
            option.innerHTML = `
                <div class="item-info">
                    <span class="item-name">${item.name}</span>
                    <input type="number" class="money-amount-input" value="1000"
                           min="1" max="1000000" placeholder="输入金额" onclick="event.stopPropagation()">
                </div>
            `;
        } else {
            option.innerHTML = `
                <div class="item-info">
                    <span class="item-name">${item.name}</span>
                </div>
            `;
        }

        option.addEventListener('click', function(e) {
            e.stopPropagation();
            selectNewItemForRate(item, itemDisplayButton, itemDropdown, weightInput, rateItem);
        });

        itemDropdown.appendChild(option);
    });

    // 添加按钮点击事件
    itemDisplayButton.addEventListener('click', function(e) {
        e.stopPropagation();
        toggleItemDropdown(itemDropdown);
    });

    // 组装物品选择器
    itemSelectorContainer.appendChild(itemDisplayButton);
    itemSelectorContainer.appendChild(itemDropdown);
    rateKey.appendChild(itemSelectorContainer);

    // 权重输入
    const rateValueInput = document.createElement('div');
    rateValueInput.className = 'rate-value-input';

    const weightInput = document.createElement('input');
    weightInput.type = 'number';
    weightInput.min = '0.001';
    weightInput.step = '0.001';
    weightInput.value = '1';
    weightInput.placeholder = '输入权重值 (0.001-1.0)';

    // 添加删除按钮
    const deleteButton = document.createElement('button');
    deleteButton.className = 'btn-delete-item';
    deleteButton.type = 'button';
    deleteButton.innerHTML = '<i class="fas fa-trash"></i>';
    deleteButton.title = '删除此物品';
    deleteButton.addEventListener('click', function() {
        rateItem.remove();
        updateRatesSummary(containerId);
        markConfigAsModified();
        showNotification('物品已删除，请点击保存配置按钮保存更改', 'info');
    });

    rateValueInput.appendChild(weightInput);
    rateValueInput.appendChild(deleteButton);

    // 组装整个项目
    rateItem.appendChild(rateKey);
    rateItem.appendChild(rateValueInput);

    // 插入到添加按钮之前
    const addButton = container.querySelector('.add-new-item-button');
    container.insertBefore(rateItem, addButton);

    // 点击其他地方关闭下拉框
    document.addEventListener('click', function(e) {
        // 如果点击的是输入框或下拉框内部，不关闭下拉框
        if (e.target.closest('.item-dropdown') ||
            e.target.closest('.item-display-button') ||
            e.target.tagName === 'INPUT') {
            return;
        }

        itemDropdown.style.display = 'none';
        itemDropdown.classList.remove('dropdown-up');
        const rateItem = itemDropdown.closest('.rate-item');
        if (rateItem) {
            rateItem.classList.remove('dropdown-open');
        }
    });

    // 页面滚动时关闭下拉框
    window.addEventListener('scroll', function() {
        if (itemDropdown.style.display === 'block') {
            itemDropdown.style.display = 'none';
            itemDropdown.classList.remove('dropdown-up');
            const rateItem = itemDropdown.closest('.rate-item');
            if (rateItem) {
                rateItem.classList.remove('dropdown-open');
            }
        }
    });
}

// 为新物品选择器选择物品
function selectNewItemForRate(selectedItem, displayButton, dropdown, weightInput, rateItem) {
    let newKey = selectedItem.code;

    // 如果是金钱物品，需要获取金额
    if (selectedItem.code === 'money') {
        const moneyInput = dropdown.querySelector('.money-amount-input');
        const amount = parseInt(moneyInput.value) || 1000;
        newKey = `money:${amount}`;
    }

    // 更新显示按钮
    const displayName = getItemDisplayName(newKey);
    displayButton.innerHTML = `
        <span class="item-name">${displayName}</span>
        <i class="fas fa-chevron-down"></i>
    `;
    displayButton.title = `物品代码: ${newKey}`;

    // 更新权重输入框的key
    weightInput.dataset.key = newKey;
    weightInput.title = `设置 ${newKey} 的权重值，数值越大出现概率越高`;

    // 移除new-item类
    rateItem.classList.remove('new-item');

    // 关闭下拉框并移除高层级类
    dropdown.style.display = 'none';
    if (rateItem) {
        rateItem.classList.remove('dropdown-open');
    }

    // 标记配置已修改
    markConfigAsModified();
}

// 获取父资源名称
function GetParentResourceName() {
    // 首先尝试使用原生函数
    try {
        if (window.invokeNative && typeof window.invokeNative === 'function') {
            // 在FiveM环境中尝试调用原生方法
            const resourceName = window.invokeNative('GET_CURRENT_RESOURCE_NAME');
            if (resourceName && typeof resourceName === 'string') {
                return resourceName;
            }
        }
    } catch (e) {
        // 错误处理
    }
    
    // 其次尝试从URL中提取资源名称
    try {
        const resourcePathMatch = window.location.href.match(/\/([^/]+)\/ui\/admin\.html/);
        if (resourcePathMatch && resourcePathMatch[1]) {
            let resourceName = resourcePathMatch[1];
            
            // 重要：如果从URL中提取的名称是"cfx-nui-xxx"格式，需要移除前缀
            if (resourceName.startsWith('cfx-nui-')) {
                resourceName = resourceName.replace('cfx-nui-', '');
            }
            
            return resourceName;
        }
    } catch (e) {
        // 错误处理
    }
    
    // 最后使用固定值作为后备
    return 'caipiaoc';
}

// 请求员工列表数据
function requestEmployeeList(callback) {
    fetch(`https://${GetParentResourceName()}/getEmployees`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP错误! 状态: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // 检查数据是否有效
        if (!data) {
            return;
        }
        
        // 确保adminData被正确更新
        if (data && data.employees) {
            if (!adminData) adminData = {};
            
            // 深度复制数据以避免引用问题
            adminData.employees = JSON.parse(JSON.stringify(data.employees));
        }
        
        // 如果有回调函数，执行回调
        if (typeof callback === 'function') {
            callback();
        }
    })
    .catch(error => {
        // 错误处理
    });
}

// 填充员工表格
function populateEmployeeTable(employees) {
    // 获取表格元素
    const tbody = document.querySelector('#employees-table-body');
    if (!tbody) {
        console.error("找不到员工表格体");
        return;
    }
    
    // 清空表格
    tbody.innerHTML = '';
    
    // 过滤出在职员工
    const activeEmployees = employees ? employees.filter(employee => employee.status !== 'fired') : [];
    
    // 填充表格
    if (activeEmployees && activeEmployees.length > 0) {
        // 处理所有员工列表
        let displayEmployees = [...activeEmployees];
        
        // 对员工列表进行排序: 按等级排序，高等级排在前面
        displayEmployees.sort((a, b) => {
            return b.level - a.level;
        });
        
        displayEmployees.forEach(employee => {
            // 创建行
            const row = document.createElement('tr');
            row.className = 'employee-row';
            row.setAttribute('data-id', employee.employee_id);
            
            // 获取等级文本 - 从职业等级数据中获取
            let levelText = `${employee.level}级`;
            if (adminData && adminData.jobGrades) {
                const jobGrade = adminData.jobGrades.find(grade => parseInt(grade.grade) === parseInt(employee.level));
                if (jobGrade) {
                    levelText = `${employee.level}级 - ${jobGrade.label}`;
                }
            }
            
            // 格式化日期为年月日形式
            const hireDate = formatDateYMD(employee.hire_date);
            const lastPayment = formatDateMDHM(employee.last_payment);
            
            // 填充单元格
            row.innerHTML = `
                <td>${employee.employee_name}</td>
                <td><span class="level-badge level-${employee.level}">${levelText}</span></td>
                <td>¥${employee.salary}</td>
                <td>${hireDate}</td>
                <td>${lastPayment || '未发放'}</td>
                <td>
                    ¥${employee.bonus || 0}
                    ${(employee.bonus && employee.bonus > 0) ? 
                        `<button class="pay-bonus-btn" data-id="${employee.employee_id}" data-name="${employee.employee_name}" title="发放奖金">
                            <i class="fas fa-hand-holding-usd"></i>
                        </button>` : 
                        ''}
                </td>
                <td class="action-cell">
                    <button class="employee-action-btn view-logs" data-id="${employee.employee_id}" data-name="${employee.employee_name}" title="查看日志">
                        <i class="fas fa-history"></i>
                    </button>
                    <button class="employee-action-btn update-level" data-id="${employee.employee_id}" title="更新等级">
                        <i class="fas fa-level-up-alt"></i>
                    </button>
                    <button class="employee-action-btn update-salary" data-id="${employee.employee_id}" title="更新薪资">
                        <i class="fas fa-money-bill-wave"></i>
                    </button>
                    <button class="employee-action-btn fee-commission" data-id="${employee.employee_id}" data-name="${employee.employee_name}" title="手续费分成设置">
                        <i class="fas fa-percentage"></i>
                    </button>
                    <button class="employee-action-btn fire" data-id="${employee.employee_id}" title="解雇员工">
                        <i class="fas fa-user-times"></i>
                    </button>
                </td>
            `;
            
            // 添加到表格
            tbody.appendChild(row);
        });
        
        // 添加事件监听器
        bindEmployeeButtons();
    } else {
        // 显示无数据提示
        tbody.innerHTML = '<tr><td colspan="7" class="text-center">暂无员工记录</td></tr>';
    }
}

// 渲染交易明细
function renderTransactions(transactions) {
    const tableBody = document.getElementById('transactions-table-body');
    if (!tableBody || !transactions) {
        return;
    }
    
    // 清空表格
    tableBody.innerHTML = '';
    
    // 检查是否有交易记录
    if (transactions.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.className = 'empty-row';
        emptyRow.innerHTML = '<td colspan="5" class="empty-message">暂无交易记录</td>';
        tableBody.appendChild(emptyRow);
        return;
    }
    
    // 按时间倒序排序
    transactions.sort((a, b) => {
        return new Date(b.timestamp) - new Date(a.timestamp);
    });
    
    // 添加每条交易记录
    transactions.forEach(transaction => {
        // 格式化时间
        const date = new Date(transaction.timestamp);
        const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
        
        // 格式化金额
        const amount = parseInt(transaction.amount) || 0;
        const formattedAmount = formatCurrency(Math.abs(amount));
        
        // 创建行
        const row = document.createElement('tr');
        row.className = 'transaction-row';
        if (transaction.is_temporary) {
            row.className += ' temp-transaction';
            row.style.backgroundColor = 'rgba(255, 255, 0, 0.1)'; // 临时记录显示淡黄色背景
        }
        row.dataset.type = transaction.transaction_type;
        row.dataset.id = transaction.id; // 添加ID属性，用于后续更新
        
        // 设置行内容
        row.innerHTML = `
            <td>${formattedDate}</td>
            <td>${getTransactionTypeText(transaction.transaction_type)}</td>
            <td class="${amount >= 0 ? 'income-text' : 'expense-text'}">
                ${amount >= 0 ? '+' : '-'}${formattedAmount}
            </td>
            <td>${transaction.description || ''}</td>
            <td>${transaction.player_name || '系统'}</td>
        `;
        
        // 添加到表格
        tableBody.appendChild(row);
    });
    
    // 设置默认过滤器为"全部"
    setTransactionFilter('all');
}

// 渲染员工列表
function renderEmployees(employees) {
    const tableBody = document.getElementById('employees-table-body');
    if (!tableBody || !employees) {
        return;
    }
    
    // 清空表格
    tableBody.innerHTML = '';
    
    // 过滤掉已解雇的员工
    const activeEmployees = employees.filter(employee => employee.status !== 'fired');
    
    // 检查是否有员工记录
    if (activeEmployees.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.className = 'empty-row';
        emptyRow.innerHTML = '<td colspan="7" class="empty-message">暂无员工记录</td>';
        tableBody.appendChild(emptyRow);
        return;
    }
    
    // 添加每个员工记录
    activeEmployees.forEach(employee => {
        // 格式化日期
        const hireDate = formatDateYMD(employee.hire_date);
        const lastPayment = formatDateMDHM(employee.last_payment);
        
        // 创建行
        const row = document.createElement('tr');
        row.className = 'employee-row';
        row.dataset.id = employee.employee_id;
        
        // 获取等级文本 - 从职业等级数据中获取
        let levelText = `${employee.level}级`;
        if (adminData && adminData.jobGrades) {
            const jobGrade = adminData.jobGrades.find(grade => parseInt(grade.grade) === parseInt(employee.level));
            if (jobGrade) {
                levelText = `${employee.level}级 - ${jobGrade.label}`;
            }
        }
        
        // 设置行内容
        row.innerHTML = `
            <td>${employee.employee_name}</td>
            <td><span class="level-badge level-${employee.level}">${levelText}</span></td>
            <td>¥${employee.salary}</td>
            <td>${hireDate}</td>
            <td>${lastPayment || '未发放'}</td>
            <td>¥${employee.bonus || 0}</td>
            <td class="action-cell">
                <button class="employee-action-btn view-logs" data-id="${employee.employee_id}" data-name="${employee.employee_name}" title="查看日志">
                    <i class="fas fa-history"></i>
                </button>
                <button class="employee-action-btn update-level" data-id="${employee.employee_id}" title="更新等级">
                    <i class="fas fa-level-up-alt"></i>
                </button>
                <button class="employee-action-btn update-salary" data-id="${employee.employee_id}" title="更新薪资">
                    <i class="fas fa-money-bill-wave"></i>
                </button>
                <button class="employee-action-btn fee-commission" data-id="${employee.employee_id}" data-name="${employee.employee_name}" title="手续费分成设置">
                    <i class="fas fa-percentage"></i>
                </button>
                <button class="employee-action-btn fire" data-id="${employee.employee_id}" title="解雇员工">
                    <i class="fas fa-user-times"></i>
                </button>
            </td>
        `;
        
        // 添加到表格
        tableBody.appendChild(row);
    });
    
    // 添加事件监听器
    addEmployeeEventListeners();
}

// 添加员工相关事件监听器
function addEmployeeEventListeners() {
    // 查看日志按钮
    const viewLogsButtons = document.querySelectorAll('.employee-action-btn.view-logs');
    viewLogsButtons.forEach(button => {
        button.addEventListener('click', function() {
            const employeeId = this.dataset.id;
            const employeeName = this.dataset.name;
            showEmployeeLogs(employeeId, employeeName);
        });
    });
    
    // 更新等级按钮
    const updateLevelButtons = document.querySelectorAll('.employee-action-btn.update-level');
    updateLevelButtons.forEach(button => {
        button.addEventListener('click', function() {
            const employeeId = this.dataset.id;
            showUpdateLevelDialog(employeeId);
        });
    });
    
    // 更新薪资按钮
    const updateSalaryButtons = document.querySelectorAll('.employee-action-btn.update-salary');
    updateSalaryButtons.forEach(button => {
        button.addEventListener('click', function() {
            const employeeId = this.dataset.id;
            showUpdateSalaryDialog(employeeId);
        });
    });
    
    // 手续费分成按钮
    const feeCommissionButtons = document.querySelectorAll('.employee-action-btn.fee-commission');
    feeCommissionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const employeeId = this.dataset.id;
            const employeeName = this.dataset.name;
            showFeeCommissionDialog(employeeId, employeeName);
        });
    });
    
    // 解雇按钮
    const fireButtons = document.querySelectorAll('.employee-action-btn.fire');
    fireButtons.forEach(button => {
        button.addEventListener('click', function() {
            const employeeId = this.dataset.id;
            showFireDialog(employeeId);
        });
    });
    
    // 移除发放奖金按钮代码，因为界面上已隐藏该按钮
    
    // 招聘员工按钮
    const hireButton = document.getElementById('hire-employee-btn');
    if (hireButton) {
        hireButton.addEventListener('click', showHireDialog);
    }
    
    // 发放薪资按钮
    const paySalaryButton = document.getElementById('pay-salary-btn');
    if (paySalaryButton) {
        paySalaryButton.addEventListener('click', payEmployeeSalary);
    }
    
    // 刷新员工状态按钮
    const refreshEmployeeBtn = document.getElementById('refresh-employee-btn');
    if (refreshEmployeeBtn) {
        refreshEmployeeBtn.removeEventListener('click', refreshEmployeeStatus);
        refreshEmployeeBtn.addEventListener('click', refreshEmployeeStatus);
    }
    
    // 对话框关闭按钮
    const closeModalButtons = document.querySelectorAll('.close-modal-btn, .cancel-btn');
    closeModalButtons.forEach(button => {
        button.addEventListener('click', function() {
            const dialog = this.closest('.modal-dialog');
            if (dialog) {
                dialog.classList.add('hidden');
            }
        });
    });
    
    // 确认招聘按钮
    const confirmHireButton = document.getElementById('confirm-hire-btn');
    if (confirmHireButton) {
        confirmHireButton.addEventListener('click', confirmHire);
    }
    
    // 确认解雇按钮
    const confirmFireButton = document.getElementById('confirm-fire-btn');
    if (confirmFireButton) {
        confirmFireButton.addEventListener('click', confirmFire);
    }
    
    // 确认更新等级按钮
    const confirmLevelButton = document.getElementById('confirm-level-btn');
    if (confirmLevelButton) {
        confirmLevelButton.addEventListener('click', confirmUpdateLevel);
    }
    
    // 确认更新薪资按钮
    const confirmSalaryButton = document.getElementById('confirm-salary-btn');
    if (confirmSalaryButton) {
        confirmSalaryButton.addEventListener('click', confirmUpdateSalary);
    }
    
    // 确认手续费分成设置按钮
    const confirmCommissionButton = document.getElementById('confirm-commission-btn');
    if (confirmCommissionButton) {
        confirmCommissionButton.addEventListener('click', confirmFeeCommission);
    }
    
    // 关闭日志按钮
    const closeLogsButton = document.getElementById('close-logs-btn');
    if (closeLogsButton) {
        closeLogsButton.addEventListener('click', function() {
            document.getElementById('employee-logs-container').classList.add('hidden');
        });
    }
}

// 刷新员工状态函数
function refreshEmployeeStatus() {
    showNotification('正在刷新员工状态...', 'info');
    
    // 请求最新员工数据
    requestEmployeeList(function() {
        // 使用当前过滤条件重新渲染
        if (adminData && adminData.employees) {
            populateEmployeeTable(adminData.employees);
            showNotification('员工状态已更新', 'success');
        }
    });
}

// 过滤员工列表
function filterEmployees(filterType) {
    console.log(`开始应用员工过滤器: ${filterType}`);
    
    // 设置活跃过滤按钮
    const filterButtons = document.querySelectorAll('#employees-tab .filter-btn');
    filterButtons.forEach(button => {
        if (button.dataset.filter === filterType) {
            button.classList.add('active');
        } else {
            button.classList.remove('active');
        }
    });
    
    // 请求最新数据
    requestEmployeeList(function() {
        if (adminData && adminData.employees) {
            // 重新渲染员工表格
            populateEmployeeTable(adminData.employees);
        }
    });
    
    console.log(`已应用员工过滤器: ${filterType}`);
}

// 显示员工日志
function showEmployeeLogs(employeeId, employeeName) {
    // 设置员工名称
    document.getElementById('employee-logs-name').textContent = employeeName;
    
    // 清空日志表格
    document.getElementById('employee-logs-body').innerHTML = '';
    
    // 请求员工日志
    fetch(`https://${GetParentResourceName()}/getEmployeeLogs`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ employeeId })
    });
}

// 渲染员工日志
function renderEmployeeLogs(employeeId, logs) {
    const tableBody = document.getElementById('employee-logs-body');
    if (!tableBody) return;
    
    // 清空表格
    tableBody.innerHTML = '';
    
    // 检查是否有日志记录
    if (!logs || logs.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = '<td colspan="4" class="empty-message">暂无操作日志</td>';
        tableBody.appendChild(emptyRow);
    } else {
        // 添加每条日志记录
        logs.forEach(log => {
            const row = document.createElement('tr');
            
            // 格式化时间
            const formattedDate = formatDateMDHM(log.timestamp);
            
            // 获取操作类型文本
            const actionText = getEmployeeActionText(log.action);
            
            row.innerHTML = `
                <td>${formattedDate}</td>
                <td>${actionText}</td>
                <td>${log.details || ''}</td>
                <td>${log.admin_name || '系统'}</td>
            `;
            
            tableBody.appendChild(row);
        });
    }
    
    // 显示日志容器
    document.getElementById('employee-logs-container').classList.remove('hidden');
}

// 获取员工操作类型文本
function getEmployeeActionText(action) {
    const actionMap = {
        'hire': '招聘',
        'rehire': '重新雇佣',
        'fire': '解雇',
        'update_level': '更新等级',
        'update_salary': '更新薪资',
        'salary': '发放薪资'
    };
    
    return actionMap[action] || action;
}

// 显示招聘对话框
function showHireDialog() {
    const dialog = document.getElementById('hire-dialog');
    if (dialog) {
        // 重置表单
        document.getElementById('nearby-players').value = '';
        document.getElementById('employee-level').value = '1';
        document.getElementById('employee-salary').value = '1000';
        document.getElementById('employee-id').value = '';
        
        // 隐藏选中的玩家信息
        document.getElementById('selected-player-info').classList.add('hidden');
        
        // 请求最新的职业等级列表
        requestJobGrades();
        
        // 获取附近玩家列表
        getNearbyPlayers();
        
        // 添加玩家选择事件监听器
        const playerSelect = document.getElementById('nearby-players');
        if (playerSelect) {
            // 移除旧的事件监听器，避免重复绑定
            playerSelect.removeEventListener('change', handlePlayerSelect);
            playerSelect.addEventListener('change', handlePlayerSelect);
        }
        
        // 添加刷新按钮事件监听器
        const refreshBtn = document.getElementById('refresh-nearby-players');
        if (refreshBtn) {
            refreshBtn.removeEventListener('click', getNearbyPlayers);
            refreshBtn.addEventListener('click', getNearbyPlayers);
        }
        
        // 显示对话框
        dialog.classList.remove('hidden');
    }
}

// 获取附近玩家
function getNearbyPlayers() {
    // 显示加载状态
    const playerSelect = document.getElementById('nearby-players');
    if (playerSelect) {
        playerSelect.innerHTML = '<option value="">加载中...</option>';
    }
    
    // 发送请求获取附近玩家
    fetch(`https://${GetParentResourceName()}/getNearbyPlayers`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.players) {
            populateNearbyPlayers(data.players);
        } else {
            // 请求失败，显示默认选项
            if (playerSelect) {
                playerSelect.innerHTML = '<option value="">-- 请选择玩家 --</option><option value="">未找到附近玩家</option>';
            }
        }
    })
    .catch(error => {
        console.error('获取附近玩家失败:', error);
        // 请求失败，显示默认选项
        if (playerSelect) {
            playerSelect.innerHTML = '<option value="">-- 请选择玩家 --</option><option value="">获取玩家失败</option>';
        }
    });
}

// 填充附近玩家列表
function populateNearbyPlayers(players) {
    const playerSelect = document.getElementById('nearby-players');
    if (!playerSelect) return;
    
    if (players.length === 0) {
        playerSelect.innerHTML = '<option value="">-- 请选择玩家 --</option><option value="">未找到附近玩家</option>';
        return;
    }
    
    let options = '<option value="">-- 请选择玩家 --</option>';
    
    players.forEach(player => {
        options += `<option value="${player.id}" data-name="${player.name}">
            ${player.name} (ID: ${player.id}) - ${player.distance}米
        </option>`;
    });
    
    playerSelect.innerHTML = options;
}

// 处理玩家选择
function handlePlayerSelect(e) {
    const select = e.target;
    const playerId = select.value;
    
    if (playerId) {
        // 获取选中的选项
        const selectedOption = select.options[select.selectedIndex];
        const playerName = selectedOption.dataset.name;
        
        // 显示选中的玩家信息
        document.getElementById('selected-player-info').classList.remove('hidden');
        document.getElementById('selected-player-name').textContent = playerName;
        document.getElementById('selected-player-id').textContent = `游戏ID: ${playerId}`;
        
        // 设置隐藏字段的值
        document.getElementById('employee-id').value = playerId;
    } else {
        // 隐藏选中的玩家信息
        document.getElementById('selected-player-info').classList.add('hidden');
        document.getElementById('employee-id').value = '';
    }
}

// 确认招聘
function confirmHire() {
    // 获取表单数据
    const employeeId = document.getElementById('employee-id').value;
    const level = parseInt(document.getElementById('employee-level').value);
    const salary = parseInt(document.getElementById('employee-salary').value);
    
    // 验证数据
    if (!employeeId) {
        showNotification('请选择要招聘的玩家', 'error');
        return;
    }
    
    if (isNaN(level) || isNaN(salary)) {
        showNotification('请输入有效的等级和薪资', 'error');
        return;
    }
    
    // 隐藏对话框
    document.getElementById('hire-dialog').classList.add('hidden');
    
    // 发送请求 - 不需要传玩家姓名，服务器会自动获取
    fetch(`https://${GetParentResourceName()}/hireEmployee`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ employeeId, level, salary })
    });
    
    // 清空表单
    document.getElementById('nearby-players').value = '';
    document.getElementById('employee-id').value = '';
    document.getElementById('selected-player-info').classList.add('hidden');
}

// 处理员工ID输入
let idVerifyTimeout = null;
function handleEmployeeIdInput(e) {
    // 此函数已不再使用，但保留定义以避免可能的引用错误
    console.log("handleEmployeeIdInput 函数已弃用");
}

// 验证玩家ID
function verifyPlayerId(playerId) {
    // 此函数已不再使用，但保留定义以避免可能的引用错误
    console.log("verifyPlayerId 函数已弃用");
}

// 检查通信通道
function checkCommunicationChannel() {
    // 此函数已不再使用，但保留定义以避免可能的引用错误
    console.log("checkCommunicationChannel 函数已弃用");
}

// 尝试请求验证状态
function tryRequestVerificationStatus(playerId) {
    // 此函数已不再使用，但保留定义以避免可能的引用错误
    console.log("tryRequestVerificationStatus 函数已弃用");
}

// 备用验证处理程序
function backupVerificationHandler(event) {
    // 此函数已不再使用，但保留定义以避免可能的引用错误
}

// 设置验证消息监听器
function setupVerificationMessageListener() {
    // 此函数已不再使用，但保留定义以避免可能的引用错误
}

// 处理验证玩家ID结果
function handleVerifyPlayerIdResult(data) {
    // 此函数已不再使用，但保留定义以避免可能的引用错误
}

// 显示解雇对话框
function showFireDialog(employeeId) {
    const dialog = document.getElementById('fire-dialog');
    if (dialog) {
        // 设置员工ID
        document.getElementById('fire-employee-id').value = employeeId;
        document.getElementById('fire-reason').value = '';
        
        // 显示对话框
        dialog.classList.remove('hidden');
    }
}

// 显示更新等级对话框
function showUpdateLevelDialog(employeeId) {
    const dialog = document.getElementById('level-dialog');
    if (dialog) {
        // 设置员工ID
        document.getElementById('level-employee-id').value = employeeId;
        
        // 请求最新的职业等级列表
        requestJobGrades();
        
        // 查找员工当前等级
        const row = document.querySelector(`.employee-row[data-id="${employeeId}"]`);
        if (row) {
            const levelBadge = row.querySelector('.level-badge');
            if (levelBadge) {
                const level = levelBadge.textContent.charAt(0);
                document.getElementById('update-level').value = level;
            }
        }
        
        // 显示对话框
        dialog.classList.remove('hidden');
    }
}

// 显示更新薪资对话框
function showUpdateSalaryDialog(employeeId) {
    const dialog = document.getElementById('salary-dialog');
    if (dialog) {
        // 设置员工ID
        document.getElementById('salary-employee-id').value = employeeId;
        
        // 查找员工当前薪资
        const row = document.querySelector(`.employee-row[data-id="${employeeId}"]`);
        if (row) {
            const salaryCell = row.querySelector('td:nth-child(3)');
            if (salaryCell) {
                const salary = salaryCell.textContent.replace(/[^\d]/g, '');
                document.getElementById('update-salary').value = salary;
            }
        }
        
        // 显示对话框
        dialog.classList.remove('hidden');
    }
}

// 确认招聘
function confirmHire() {
    // 获取表单数据
    const employeeId = document.getElementById('employee-id').value;
    const level = parseInt(document.getElementById('employee-level').value);
    const salary = parseInt(document.getElementById('employee-salary').value);
    
    // 验证数据
    if (!employeeId) {
        showNotification('请输入有效的员工ID', 'error');
        return;
    }
    
    // 检查ID是否已经通过验证
    const nameStatus = document.getElementById('employee-name-status');
    if (nameStatus && nameStatus.className.indexOf('text-success') === -1) {
        showNotification('请先验证员工ID是否有效', 'error');
        return;
    }
    
    if (isNaN(level) || isNaN(salary)) {
        showNotification('请输入有效的等级和薪资', 'error');
        return;
    }
    
    // 隐藏对话框
    document.getElementById('hire-dialog').classList.add('hidden');
    
    // 发送请求 - 不需要传玩家姓名，服务器会自动获取
    fetch(`https://${GetParentResourceName()}/hireEmployee`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ employeeId, level, salary })
    });
    
    // 清空表单
    document.getElementById('employee-id').value = '';
    document.getElementById('employee-salary').value = '';
}

// 确认解雇
function confirmFire() {
    // 获取表单数据
    const employeeId = document.getElementById('fire-employee-id').value;
    const reason = document.getElementById('fire-reason').value;
    
    // 验证数据
    if (!employeeId) {
        showNotification('员工ID无效', 'error');
        return;
    }
    
    // 隐藏对话框
    document.getElementById('fire-dialog').classList.add('hidden');
    
    // 获取员工名称，用于通知
    let employeeName = '';
    if (adminData && adminData.employees) {
        const employee = adminData.employees.find(e => e.employee_id === employeeId);
        if (employee) {
            employeeName = employee.employee_name;
            
            // 从全局数据中移除该员工
            adminData.employees = adminData.employees.filter(e => e.employee_id !== employeeId);
        }
    }
    
    // 从DOM中移除该员工行
    const row = document.querySelector(`.employee-row[data-id="${employeeId}"]`);
    if (row) {
        row.remove();
    }
    
    // 显示通知
    showNotification(`已解雇 ${employeeName || '员工'}`, 'success');
    
    // 发送请求
    fetch(`https://${GetParentResourceName()}/fireEmployee`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ employeeId, reason })
    });
}

// 更新员工状态（不再使用）
function updateEmployeeStatus(employeeId, newStatus, employeeName) {
    // 该函数已不再使用，保留函数结构以避免引用错误
    return;
}

// 确认更新等级
function confirmUpdateLevel() {
    // 获取表单数据
    const employeeId = document.getElementById('level-employee-id').value;
    const newLevel = parseInt(document.getElementById('update-level').value);
    
    // 验证数据
    if (!employeeId || isNaN(newLevel)) {
        showNotification('请选择有效的等级', 'error');
        return;
    }
    
    // 隐藏对话框
    document.getElementById('level-dialog').classList.add('hidden');
    
    // 获取员工名称，用于本地临时更新
    let employeeName = '';
    if (adminData && adminData.employees) {
        const employee = adminData.employees.find(e => e.employee_id === employeeId);
        if (employee) {
            employeeName = employee.employee_name;
        }
    }
    
    // 本地临时更新，提高用户体验
    refreshEmployeeLevel(employeeId, newLevel, employeeName);
    
    // 发送请求
    fetch(`https://${GetParentResourceName()}/updateEmployeeLevel`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ employeeId, newLevel })
    });
}

// 确认更新薪资
function confirmUpdateSalary() {
    // 获取表单数据
    const employeeId = document.getElementById('salary-employee-id').value;
    const newSalary = parseInt(document.getElementById('update-salary').value);
    
    // 验证数据
    if (!employeeId || isNaN(newSalary)) {
        showNotification('请输入有效的薪资', 'error');
        return;
    }
    
    // 隐藏对话框
    document.getElementById('salary-dialog').classList.add('hidden');
    
    // 获取员工名称，用于本地临时更新
    let employeeName = '';
    if (adminData && adminData.employees) {
        const employee = adminData.employees.find(e => e.employee_id === employeeId);
        if (employee) {
            employeeName = employee.employee_name;
            
            // 本地临时更新薪资显示
            const row = document.querySelector(`.employee-row[data-id="${employeeId}"]`);
            if (row) {
                const salaryCell = row.querySelector('td:nth-child(3)');
                if (salaryCell) {
                    salaryCell.textContent = `¥${newSalary}`;
                    salaryCell.classList.add('highlight-update');
                    setTimeout(() => {
                        salaryCell.classList.remove('highlight-update');
                    }, 2000);
                }
            }
            
            // 更新adminData中的数据
            const employeeIndex = adminData.employees.findIndex(e => e.employee_id === employeeId);
            if (employeeIndex !== -1) {
                adminData.employees[employeeIndex].salary = newSalary;
            }
            
            showNotification(`已更新 ${employeeName} 的薪资为 ¥${newSalary}`, 'success');
        }
    }
    
    // 发送请求
    fetch(`https://${GetParentResourceName()}/updateEmployeeSalary`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ employeeId, newSalary })
    });
}

// 发放薪资函数
function payEmployeeSalary() {
    console.log("开始发放薪资");
    showNotification('正在发放薪资...', 'info');
    
    // 发送请求到服务器
    fetch(`https://${GetParentResourceName()}/payEmployeeSalary`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
    }).catch(err => {
        console.error("发放薪资失败:", err);
    });
}

// 发放奖金函数
function payEmployeeBonus(employeeId, employeeName) {
    console.log("开始发放奖金给员工:", employeeId, employeeName);
    showNotification(`正在发放奖金给${employeeName}...`, 'info');
    
    // 发送请求到服务器
    fetch(`https://${GetParentResourceName()}/payEmployeeBonus`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            employeeId: employeeId
        })
    }).catch(err => {
        console.error("发放奖金失败:", err);
    });
    
    // 立即更新UI显示(发放后清零)
    refreshEmployeeBonus(employeeId, 0, employeeName);
    
    // 如果adminData中有对应的员工数据，也更新它
    if (adminData && adminData.employees) {
        const employeeIndex = adminData.employees.findIndex(e => e.employee_id === employeeId);
        if (employeeIndex !== -1) {
            adminData.employees[employeeIndex].bonus = 0;
        }
    }
    
    // 显示成功通知
    showNotification(`已成功发放奖金给 ${employeeName}`, 'success');
}

// 获取交易类型文本
function getTransactionTypeText(type) {
    const typeMap = {
        'deposit': '存款',
        'withdraw': '取款',
        'sales': '销售',
        'prize': '兑奖',
        'commission': '手续费分成',
        'bonus_payment': '奖金发放',
        'fee': '手续费'
    };
    
    return typeMap[type] || type;
}

// 格式化货币金额
function formatCurrency(amount) {
    return amount.toLocaleString('zh-CN', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    });
}

// 渲染职业等级选项
function renderJobGradeOptions(jobGrades) {
    if (!jobGrades || jobGrades.length === 0) {
        return;
    }
    
    // 获取所有需要更新的等级选择框
    const levelSelects = [
        document.getElementById('employee-level'),
        document.getElementById('update-level')
    ];
    
    // 更新每个选择框
    levelSelects.forEach(select => {
        if (!select) return;
        
        // 清空现有选项
        select.innerHTML = '';
        
        // 添加新选项
        jobGrades.forEach(grade => {
            const option = document.createElement('option');
            option.value = grade.grade;
            option.textContent = `${grade.grade}级 - ${grade.label}`;
            select.appendChild(option);
        });
    });
}

// 请求职业等级列表
function requestJobGrades() {
    fetch(`https://${GetParentResourceName()}/getJobGrades`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
    })
    .catch(error => {
        // 错误处理
    });
}

// 立即更新特定员工的等级显示
function refreshEmployeeLevel(employeeId, newLevel, employeeName) {
    if (!employeeId || newLevel === undefined) return;
    
    // 查找员工行
    let row = document.querySelector(`.employee-row[data-id="${employeeId}"]`);
    if (!row) return;
    
    // 查找等级徽章元素
    const levelBadge = row.querySelector('.level-badge');
    if (!levelBadge) return;
    
    // 获取职业等级名称
    let gradeName = `${newLevel}级`;
    if (adminData && adminData.jobGrades) {
        const grade = adminData.jobGrades.find(g => parseInt(g.grade) === parseInt(newLevel));
        if (grade && grade.label) {
            gradeName = grade.label;
        }
    }
    
    // 更新等级徽章
    levelBadge.textContent = `${newLevel}级 - ${gradeName}`;
    levelBadge.className = 'level-badge'; // 重置类名
    levelBadge.classList.add(`level-${newLevel}`); // 添加新的等级类
    
    // 添加闪烁效果以突出显示更改
    levelBadge.classList.add('highlight-update');
    setTimeout(() => {
        levelBadge.classList.remove('highlight-update');
    }, 2000);
    
    // 显示通知
    showNotification(`已更新 ${employeeName || '员工'} 的等级为 ${newLevel}级 - ${gradeName}`, 'success');
    
    // 如果adminData中有对应的员工数据，也更新它
    if (adminData && adminData.employees) {
        const employeeIndex = adminData.employees.findIndex(e => e.employee_id === employeeId);
        if (employeeIndex !== -1) {
            adminData.employees[employeeIndex].level = newLevel;
        }
    }
}

// 立即更新特定员工的薪资显示
function refreshEmployeeSalary(employeeId, newSalary, employeeName) {
    if (!employeeId || !newSalary) return;
    
    // 查找员工行
    let row = document.querySelector(`.employee-row[data-id="${employeeId}"]`);
    if (!row) {
        // 尝试在员工表格中查找
        const employeeTable = document.getElementById('employees-table-body');
        if (employeeTable) {
            const rows = employeeTable.querySelectorAll(`.employee-row`);
            
            // 遍历所有行，查找匹配的员工ID
            rows.forEach(r => {
                if (r.dataset.id === employeeId) {
                    row = r;
                }
            });
        }
        
        if (!row) {
            return;
        }
    }
    
    // 查找薪资单元格
    const salaryCell = row.querySelector('td:nth-child(3)');
    if (!salaryCell) return;
    
    // 更新薪资显示
    salaryCell.textContent = `¥${newSalary}`;
    
    // 添加闪烁效果以突出显示更改
    salaryCell.classList.add('highlight-update');
    setTimeout(() => {
        salaryCell.classList.remove('highlight-update');
    }, 2000);
    
    // 显示通知
    showNotification(`已更新 ${employeeName || '员工'} 的薪资为 ¥${newSalary}`, 'success');
    
    // 如果adminData中有对应的员工数据，也更新它
    if (adminData && adminData.employees) {
        const employeeIndex = adminData.employees.findIndex(e => e.employee_id === employeeId);
        if (employeeIndex !== -1) {
            adminData.employees[employeeIndex].salary = newSalary;
        }
    }
}

// 立即更新特定员工的奖金显示
function refreshEmployeeBonus(employeeId, newBonus, employeeName) {
    if (!employeeId) return;
    
    // 查找员工行
    let row = document.querySelector(`.employee-row[data-id="${employeeId}"]`);
    if (!row) {
        // 尝试在员工表格中查找
        const employeeTable = document.getElementById('employees-table-body');
        if (employeeTable) {
            const rows = employeeTable.querySelectorAll(`.employee-row`);
            
            // 遍历所有行，查找匹配的员工ID
            rows.forEach(r => {
                if (r.dataset.id === employeeId) {
                    row = r;
                }
            });
        }
        
        if (!row) {
            return;
        }
    }
    
    // 查找奖金单元格
    const bonusCell = row.querySelector('td:nth-child(6)');
    if (!bonusCell) return;
    
    // 格式化奖金值为数字
    newBonus = parseInt(newBonus) || 0;
    
    // 更新奖金显示 - 隐藏按钮，只显示金额
    bonusCell.textContent = `¥${newBonus}`;
    
    // 添加闪烁效果以突出显示更改
    bonusCell.classList.add('highlight-update');
    setTimeout(() => {
        bonusCell.classList.remove('highlight-update');
    }, 2000);
    
    // 显示通知
    showNotification(`已更新 ${employeeName || '员工'} 的奖金为 ¥${newBonus}`, 'success');
    
    // 如果adminData中有对应的员工数据，也更新它
    if (adminData && adminData.employees) {
        const employeeIndex = adminData.employees.findIndex(e => e.employee_id === employeeId);
        if (employeeIndex !== -1) {
            adminData.employees[employeeIndex].bonus = newBonus;
        }
    }
}

// 处理直接员工更新
function handleDirectEmployeeUpdate(updateType, employee) {
    console.log(`处理员工直接更新: 类型=${updateType}, 员工=${employee?.employee_name || employee?.employeeId || '未知'}`);
    
    if (!employee && updateType !== 'delete' && !(updateType.type === 'delete')) {
        console.error("无效的员工数据");
        return;
    }
    
    // 根据更新类型处理
    switch (updateType.type || updateType) {
        case 'new':
            // 新员工，添加到员工列表
            console.log("处理新员工添加");
            if (adminData && adminData.employees) {
                // 确保不重复添加
                const existingIndex = adminData.employees.findIndex(e => e.employee_id === employee.employee_id);
                if (existingIndex !== -1) {
                    adminData.employees[existingIndex] = employee;
                } else {
                    adminData.employees.unshift(employee);
                }
            }
            
            // 重新渲染员工列表
            if (adminData && adminData.employees) {
                populateEmployeeTable(adminData.employees);
            }
            
            // 显示通知
            showNotification(`已添加新员工: ${employee.employee_name}`, 'success');
            break;
            
        case 'level':
            // 更新员工等级
            console.log("处理员工等级更新");
            updateEmployeeInTable(employee, 'level');
            break;
            
        case 'salary':
            // 更新员工薪资
            console.log("处理员工薪资更新");
            updateEmployeeInTable(employee, 'salary');
            break;
            
        case 'bonus':
            // 更新员工奖金
            console.log("处理员工奖金更新");
            updateEmployeeInTable(employee, 'bonus');
            break;
            
        case 'last_payment':
            // 更新员工上次发薪时间
            console.log("处理员工上次发薪时间更新");
            updateEmployeeInTable(employee, 'last_payment');
            break;
            
        case 'status':
            // 更新员工状态（解雇等）
            console.log("处理员工状态更新");
            if (employee.status === 'fired') {
                // 员工被解雇，从表格中移除
                removeEmployeeFromTable(employee.employee_id);
                
                // 更新adminData
                if (adminData && adminData.employees) {
                    const index = adminData.employees.findIndex(e => e.employee_id === employee.employee_id);
                    if (index !== -1) {
                        adminData.employees[index].status = 'fired';
                    }
                }
                
                // 显示通知
                showNotification(`已解雇员工: ${employee.employee_name}`, 'success');
            } else {
                // 其他状态更新
                updateEmployeeInTable(employee, 'status');
            }
            break;
            
        case 'delete':
            // 员工被解雇并从数据库删除
            console.log("处理员工删除");
            const employeeId = employee.employeeId || employee.employee_id;
            
            // 从表格中移除
            removeEmployeeFromTable(employeeId);
            
            // 更新adminData，直接从数组中删除
            if (adminData && adminData.employees) {
                const index = adminData.employees.findIndex(e => e.employee_id === employeeId);
                if (index !== -1) {
                    // 保存被删除员工的名称用于通知
                    const employeeName = adminData.employees[index].employee_name;
                    
                    // 从数组中移除
                    adminData.employees.splice(index, 1);
                    
                    // 显示通知
                    showNotification(`已删除员工数据: ${employeeName}`, 'success');
                }
            }
            break;
            
        case 'rehire':
            // 重新雇佣员工
            console.log("处理员工重新雇佣");
            if (adminData && adminData.employees) {
                // 查找员工
                const index = adminData.employees.findIndex(e => e.employee_id === employee.employee_id);
                if (index !== -1) {
                    // 更新状态
                    adminData.employees[index].status = 'active';
                    adminData.employees[index].level = employee.level;
                    adminData.employees[index].salary = employee.salary;
                } else {
                    // 添加新员工
                    adminData.employees.unshift(employee);
                }
            }
            
            // 重新渲染员工列表
            if (adminData && adminData.employees) {
                populateEmployeeTable(adminData.employees);
            }
            
            // 显示通知
            showNotification(`已重新雇佣员工: ${employee.employee_name}`, 'success');
            break;
            
        default:
            console.warn(`未知的更新类型: ${updateType}`);
    }
}

// 更新表格中的员工信息
function updateEmployeeInTable(employee, updateField) {
    console.log(`更新表格中的员工信息: ${employee.employee_name}, 字段=${updateField}`);
    
    // 查找员工行
    const row = document.querySelector(`.employee-row[data-id="${employee.employee_id}"]`);
    if (!row) {
        console.warn(`找不到员工行: ${employee.employee_id}`);
        
        // 检查是否需要重新渲染整个表格
        if (adminData && adminData.employees) {
            // 更新adminData中的员工数据
            const index = adminData.employees.findIndex(e => e.employee_id === employee.employee_id);
            if (index !== -1) {
                // 更新特定字段
                adminData.employees[index][updateField] = employee[updateField];
            }
            
            // 重新渲染员工表格
            populateEmployeeTable(adminData.employees);
        }
        
        return;
    }
    
    // 更新adminData中的员工数据
    if (adminData && adminData.employees) {
        const index = adminData.employees.findIndex(e => e.employee_id === employee.employee_id);
        if (index !== -1) {
            // 更新特定字段
            adminData.employees[index][updateField] = employee[updateField];
        }
    }
    
    // 根据更新字段更新UI
    switch (updateField) {
        case 'level':
            // 更新等级显示
            const levelCell = row.querySelector('td:nth-child(2)');
            if (levelCell) {
                // 获取等级文本
                let levelText = `${employee.level}级`;
                if (adminData && adminData.jobGrades) {
                    const jobGrade = adminData.jobGrades.find(grade => parseInt(grade.grade) === parseInt(employee.level));
                    if (jobGrade) {
                        levelText = `${employee.level}级 - ${jobGrade.label}`;
                    }
                }
                
                levelCell.innerHTML = `<span class="level-badge level-${employee.level}">${levelText}</span>`;
                
                // 添加高亮效果
                levelCell.classList.add('highlight-update');
                setTimeout(() => {
                    levelCell.classList.remove('highlight-update');
                }, 2000);
            }
            break;
            
        case 'salary':
            // 更新薪资显示
            const salaryCell = row.querySelector('td:nth-child(3)');
            if (salaryCell) {
                salaryCell.textContent = `¥${employee.salary}`;
                
                // 添加高亮效果
                salaryCell.classList.add('highlight-update');
                setTimeout(() => {
                    salaryCell.classList.remove('highlight-update');
                }, 2000);
            }
            break;
            
        case 'last_payment':
            // 更新上次发薪时间显示
            const lastPaymentCell = row.querySelector('td:nth-child(5)');
            if (lastPaymentCell) {
                lastPaymentCell.textContent = formatDateMDHM(employee.last_payment);
                
                // 添加高亮效果
                lastPaymentCell.classList.add('highlight-update');
                setTimeout(() => {
                    lastPaymentCell.classList.remove('highlight-update');
                }, 2000);
            }
            break;
            
        case 'bonus':
            // 更新奖金显示
            const bonusCell = row.querySelector('td:nth-child(6)');
            if (bonusCell) {
                const bonus = parseInt(employee.bonus) || 0;
                
                // 更新奖金显示 - 隐藏按钮，只显示金额
                bonusCell.textContent = `¥${bonus}`;
                
                // 添加高亮效果
                bonusCell.classList.add('highlight-update');
                setTimeout(() => {
                    bonusCell.classList.remove('highlight-update');
                }, 2000);
            }
            break;
    }
}

// 从表格中移除员工
function removeEmployeeFromTable(employeeId) {
    console.log(`从表格中移除员工: ${employeeId}`);
    
    // 查找员工行
    const row = document.querySelector(`.employee-row[data-id="${employeeId}"]`);
    if (row) {
        // 添加移除动画
        row.classList.add('fade-out');
        
        // 延迟后移除
        setTimeout(() => {
            row.remove();
            
            // 检查是否需要显示"无数据"提示
            const remainingRows = document.querySelectorAll('#employees-table-body .employee-row');
            if (remainingRows.length === 0) {
                const tableBody = document.getElementById('employees-table-body');
                if (tableBody) {
                    tableBody.innerHTML = '<tr><td colspan="7" class="text-center">暂无员工记录</td></tr>';
                }
            }
        }, 500);
    }
}

// 绑定员工管理按钮事件
function bindEmployeeButtons() {
    // 查看日志按钮
    document.querySelectorAll('.employee-action-btn.view-logs').forEach(button => {
        button.removeEventListener('click', handleViewLogsClick);
        button.addEventListener('click', handleViewLogsClick);
    });
    
    // 更新等级按钮
    document.querySelectorAll('.employee-action-btn.update-level').forEach(button => {
        button.removeEventListener('click', handleUpdateLevelClick);
        button.addEventListener('click', handleUpdateLevelClick);
    });
    
    // 更新薪资按钮
    document.querySelectorAll('.employee-action-btn.update-salary').forEach(button => {
        button.removeEventListener('click', handleUpdateSalaryClick);
        button.addEventListener('click', handleUpdateSalaryClick);
    });
    
    // 解雇按钮
    document.querySelectorAll('.employee-action-btn.fire').forEach(button => {
        button.removeEventListener('click', handleFireClick);
        button.addEventListener('click', handleFireClick);
    });
    
    // 刷新状态按钮
    const refreshEmployeeBtn = document.getElementById('refresh-employee-btn');
    if (refreshEmployeeBtn) {
        refreshEmployeeBtn.removeEventListener('click', requestEmployeeList);
        refreshEmployeeBtn.addEventListener('click', function() {
            requestEmployeeList();
            showNotification('正在刷新员工状态...', 'info');
        });
    }
    
    // 招聘员工按钮
    const hireEmployeeBtn = document.getElementById('hire-employee-btn');
    if (hireEmployeeBtn) {
        hireEmployeeBtn.removeEventListener('click', showHireDialog);
        hireEmployeeBtn.addEventListener('click', showHireDialog);
    }
    
    // 发放薪资按钮
    const paySalaryBtn = document.getElementById('pay-salary-btn');
    if (paySalaryBtn) {
        paySalaryBtn.removeEventListener('click', payEmployeeSalary);
        paySalaryBtn.addEventListener('click', payEmployeeSalary);
    }
}

// 处理更新等级点击事件
function handleUpdateLevelClick(event) {
    const button = event.target;
    const id = button.dataset.id;
    const name = button.dataset.name;
    
    // 设置对话框数据
    document.getElementById('level-employee-id').value = id;
    document.getElementById('level-employee-name').value = name;
    
    // 显示对话框
    document.getElementById('level-dialog').classList.remove('hidden');
}

// 处理更新薪资点击事件
function handleUpdateSalaryClick(event) {
    const button = event.target;
    const id = button.dataset.id;
    const name = button.dataset.name;
    
    // 设置对话框数据
    document.getElementById('salary-employee-id').value = id;
    document.getElementById('salary-employee-name').value = name;
    
    // 显示对话框
    document.getElementById('salary-dialog').classList.remove('hidden');
}

// 处理切换状态点击事件 - 不再需要此功能，服务器自动获取在线状态
function handleToggleStatusClick(event) {
    // 移除此功能，因为服务器会自动检测员工在线状态
    event.preventDefault();
    showNotification('员工在线状态由服务器自动检测，无法手动切换', 'info');
}

// 处理解雇点击事件
function handleFireClick(event) {
    const button = event.target;
    const id = button.dataset.id;
    const name = button.dataset.name;
    
    // 设置对话框数据
    document.getElementById('fire-employee-id').value = id;
    document.getElementById('fire-employee-name').value = name;
    
    // 显示对话框
    document.getElementById('fire-dialog').classList.remove('hidden');
}

// 处理查看日志点击事件
function handleViewLogsClick(event) {
    const button = event.target;
    const id = button.dataset.id;
    const name = button.dataset.name;
    
    // 请求员工日志
    document.getElementById('logs-employee-name').textContent = name;
    document.getElementById('employee-logs').innerHTML = '<div class="loading">加载中...</div>';
    document.getElementById('logs-dialog').classList.remove('hidden');
    
    // 发送请求
    $.post(`https://${GetParentResourceName()}/getEmployeeLogs`, {
        employeeId: id
    });
}

// 移除此函数，因为服务器会自动检测员工在线状态
function toggleEmployeeStatus(employeeId, newStatus) {
    // 此功能已弃用，由服务器自动检测员工在线状态
    showNotification('员工在线状态由服务器自动检测，无法手动切换', 'info');
}

// 格式化日期为年月日形式
function formatDateYMD(dateString) {
    if (!dateString) return '';
    
    try {
        // 处理时间戳格式
        if (typeof dateString === 'string' && dateString.length > 10 && !isNaN(dateString)) {
            // 如果是13位时间戳（如：*************）
            const timestamp = parseInt(dateString);
            const date = new Date(timestamp);
            
            // 检查日期是否有效
            if (isNaN(date.getTime())) return dateString;
            
            // 返回YYYY年MM月DD日格式
            return date.getFullYear() + '年' + 
                   padZero(date.getMonth() + 1) + '月' + 
                   padZero(date.getDate()) + '日';
        }
        
        // 处理普通日期字符串
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return dateString;
        
        // 返回YYYY年MM月DD日格式
        return date.getFullYear() + '年' + 
               padZero(date.getMonth() + 1) + '月' + 
               padZero(date.getDate()) + '日';
    } catch (e) {
        return dateString;
    }
}

// 显示手续费分成设置对话框
function showFeeCommissionDialog(employeeId, employeeName) {
    // 设置对话框标题
    const dialogHeader = document.querySelector('#fee-commission-dialog .modal-header h3');
    if (dialogHeader) {
        dialogHeader.textContent = `手续费分成设置 - ${employeeName}`;
    }
    
    // 设置员工ID
    document.getElementById('commission-employee-id').value = employeeId;
    
    // 获取员工当前的手续费分成设置
    fetch(`https://${GetParentResourceName()}/caipiaoc:getEmployeeCommissionSettings`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ employeeId: employeeId })
    }).catch(err => {
        // 错误处理
    });
    
    // 显示对话框
    document.getElementById('fee-commission-dialog').classList.remove('hidden');
}

// 确认手续费分成设置
function confirmFeeCommission() {
    const employeeId = document.getElementById('commission-employee-id').value;
    const enabled = document.getElementById('fee-commission-enabled').checked;
    const ratePercent = parseFloat(document.getElementById('fee-commission-rate').value);
    const minAmount = parseInt(document.getElementById('fee-commission-min').value);
    
    // 验证输入
    if (isNaN(ratePercent) || ratePercent < 0 || ratePercent > 100) {
        showNotification("分成比例必须在0-100之间", "error");
        return;
    }
    
    if (isNaN(minAmount) || minAmount < 0) {
        showNotification("最小手续费金额必须大于等于0", "error");
        return;
    }
    
    const settings = {
        employeeId: employeeId,
        enabled: enabled,
        rate: ratePercent / 100,  // 转换为小数
        minFeeAmount: minAmount
    };
    
    // 发送到服务器
    fetch(`https://${GetParentResourceName()}/caipiaoc:saveEmployeeCommissionSettings`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification("手续费分成设置已保存", "success");
        } else {
            showNotification("保存设置失败: " + (data.message || "未知错误"), "error");
        }
    })
    .catch(err => {
        showNotification("保存设置失败", "error");
    });
    
    // 隐藏对话框
    document.getElementById('fee-commission-dialog').classList.add('hidden');
}

// 更新手续费分成设置UI
function updateEmployeeCommissionSettingsUI(settings) {
    if (!settings) return;
    
    document.getElementById('fee-commission-enabled').checked = settings.enabled;
    document.getElementById('fee-commission-rate').value = Math.round(settings.rate * 100);
    document.getElementById('fee-commission-min').value = settings.minFeeAmount;
}

// 设置消息接收处理
window.addEventListener('message', function(event) {
    // 获取事件数据
    const data = event.data;
    
    // 如果没有action属性，则忽略
    if (!data.action) return;
    
    // 根据action处理不同的事件
    switch (data.action) {
        case 'openAdminUI':
            // 打开管理界面
            openAdminSystem();
            break;
            
        case 'hideFrame':
            // 隐藏iframe
            closeAdminSystem();
            break;
            
        case 'refreshAdminData':
            // 刷新管理系统数据
            requestAdminData();
            break;
            
        case 'updateCommissionSettings':
            // 更新手续费分成设置
            if (data.success) {
                updateEmployeeCommissionSettingsUI(data.settings);
                showNotification("手续费分成设置已更新", "success");
            } else {
                showNotification("更新手续费分成设置失败", "error");
            }
            break;
            
        case 'employeeUpdate':
            // 员工数据更新（如奖金变更）
            if (data.data && data.data.type === 'bonus') {
                // 获取员工数据
                const employeeId = data.data.employee_id;
                const employeeName = data.data.employee_name;
                
                // 如果adminData已存在，更新员工奖金
                if (adminData && adminData.employees) {
                    const employee = adminData.employees.find(e => e.employee_id === employeeId);
                    if (employee) {
                        // 获取当前奖金
                        const currentBonus = parseInt(employee.bonus) || 0;
                        // 计算新奖金(增加值)
                        const bonusAddition = parseInt(data.data.bonus) || 0;
                        const newBonus = currentBonus + bonusAddition;
                        
                        // 更新员工数据
                        employee.bonus = newBonus;
                        
                        // 更新UI显示
                        refreshEmployeeBonus(employeeId, newBonus, employeeName);
                    }
                }
            }
            break;
    }
});

// 格式化日期为月日时分格式(不含年份)
function formatDateMDHM(dateString) {
    if (!dateString) return '';
    
    try {
        // 处理时间戳格式
        if (typeof dateString === 'string' && dateString.length > 10 && !isNaN(dateString)) {
            // 如果是13位时间戳（如：*************）
            const timestamp = parseInt(dateString);
            const date = new Date(timestamp);
            
            // 检查日期是否有效
            if (isNaN(date.getTime())) return dateString;
            
            // 返回MM月DD日 HH:MM格式
            return padZero(date.getMonth() + 1) + '月' + 
                  padZero(date.getDate()) + '日 ' + 
                  padZero(date.getHours()) + ':' + 
                  padZero(date.getMinutes());
        }
        
        // 处理普通日期字符串
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return dateString;
        
        // 返回MM月DD日 HH:MM格式
        return padZero(date.getMonth() + 1) + '月' + 
               padZero(date.getDate()) + '日 ' + 
               padZero(date.getHours()) + ':' + 
               padZero(date.getMinutes());
    } catch (e) {
        return dateString;
    }
}

// 获取彩票配置
function getLotteryConfig() {
    console.log("请求获取彩票配置数据");
    
    // 发送事件到服务器，请求彩票配置数据
    fetch(`https://${GetParentResourceName()}/getLotteryConfig`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
    })
    .catch(error => {
        console.error("获取彩票配置请求失败:", error);
        showNotification('获取配置失败，请检查控制台', 'error');
    });
}

// 更新彩票配置UI（不切换标签页）
function updateLotteryConfigUIWithoutTabSwitch(config) {
    if (!config) return;

    if (debugMode) {
        console.log("正在更新彩票配置UI（不切换标签页）:", config);
    }

    // 存储配置数据到全局变量，供其他函数使用
    window.lotteryConfigData = config;

    // 刷新所有已显示的物品名称
    refreshAllItemDisplayNames();

    // 处理双色球配置
    if (config.double_ball) {
        console.log("更新双色球配置:", config.double_ball);

        const priceInput = document.getElementById('double-ball-price');
        const probInput = document.getElementById('double-ball-prob');

        if (priceInput) {
            priceInput.value = config.double_ball.price || '';
            console.log("设置双色球价格:", config.double_ball.price);
        } else {
            console.error("找不到双色球价格输入框元素");
        }

        if (probInput) {
            probInput.value = config.double_ball.probability || '';
            console.log("设置双色球概率:", config.double_ball.probability);
        } else {
            console.error("找不到双色球概率输入框元素");
        }

        // 更新开奖设置
        if (config.double_ball.drawTime) {
            const hourInput = document.getElementById('double-ball-draw-hour');
            const minuteInput = document.getElementById('double-ball-draw-minute');

            if (hourInput) {
                hourInput.value = config.double_ball.drawTime.hour || 0;
            }
            if (minuteInput) {
                minuteInput.value = config.double_ball.drawTime.minute || 0;
            }
        }

        // 更新开奖日期复选框
        if (config.double_ball.drawDays && Array.isArray(config.double_ball.drawDays)) {
            // 先清空所有复选框
            for (let i = 1; i <= 7; i++) {
                const checkbox = document.getElementById(`double-ball-day-${i}`);
                if (checkbox) checkbox.checked = false;
            }

            // 然后根据配置选中对应的复选框
            config.double_ball.drawDays.forEach(day => {
                const checkbox = document.getElementById(`double-ball-day-${day}`);
                if (checkbox) checkbox.checked = true;
            });
        }

        // 渲染双色球奖项 - 直接使用配置文件中的数据
        if (config.double_ball.prizes && Object.keys(config.double_ball.prizes).length > 0) {
            console.log("使用配置文件中的双色球奖项数据");
            renderPrizeList('double-ball-prizes', config.double_ball.prizes);
        } else {
            console.log("配置文件中没有双色球奖项数据，使用默认数据");
            // 只有在配置文件中确实没有数据时才使用默认数据
            const defaultPrizes = createDefaultPrizes('double_ball');
            renderPrizeList('double-ball-prizes', defaultPrizes);
        }
    }

    // 处理刮刮乐配置 - 使用专门的更新函数
    if (config.scratch_cards) {
        if (debugMode) {
            console.log("更新刮刮乐配置UI:", config.scratch_cards);
        }
        updateScratchCardsUI(config.scratch_cards);
    }

    // 处理刮刮乐符号配置 - 使用专门的更新函数
    if (config.scratch_symbols) {
        if (debugMode) {
            console.log("更新刮刮乐符号配置UI:", config.scratch_symbols);
        }
        updateScratchSymbolsUI(config.scratch_symbols);
    }

    // 处理大乐透配置
    if (config.super_lotto) {
        if (debugMode) {
            console.log("更新大乐透配置:", config.super_lotto);
        }

        const priceInput = document.getElementById('super-lotto-price');
        const probInput = document.getElementById('super-lotto-prob');

        if (priceInput) {
            priceInput.value = config.super_lotto.price || '';
            if (debugMode) {
                console.log("设置大乐透价格:", config.super_lotto.price);
            }
        } else {
            if (debugMode) {
                console.error("找不到大乐透价格输入框元素");
            }
        }

        if (probInput) {
            probInput.value = config.super_lotto.probability || '';
            if (debugMode) {
                console.log("设置大乐透概率:", config.super_lotto.probability);
            }
        } else {
            if (debugMode) {
                console.error("找不到大乐透概率输入框元素");
            }
        }

        // 更新开奖设置
        if (config.super_lotto.drawTime) {
            document.getElementById('super-lotto-draw-hour').value = config.super_lotto.drawTime.hour || 0;
            document.getElementById('super-lotto-draw-minute').value = config.super_lotto.drawTime.minute || 0;
        }

        // 更新开奖日期复选框
        if (config.super_lotto.drawDays && Array.isArray(config.super_lotto.drawDays)) {
            // 先清空所有复选框
            for (let i = 1; i <= 7; i++) {
                const checkbox = document.getElementById(`super-lotto-day-${i}`);
                if (checkbox) checkbox.checked = false;
            }

            // 然后根据配置选中对应的复选框
            config.super_lotto.drawDays.forEach(day => {
                const checkbox = document.getElementById(`super-lotto-day-${day}`);
                if (checkbox) checkbox.checked = true;
            });
        }

        // 渲染大乐透奖项 - 直接使用配置文件中的数据
        if (config.super_lotto.prizes && Object.keys(config.super_lotto.prizes).length > 0) {
            if (debugMode) {
                console.log("使用配置文件中的大乐透奖项数据");
            }
            renderPrizeList('super-lotto-prizes', config.super_lotto.prizes);
        } else {
            if (debugMode) {
                console.log("配置文件中没有大乐透奖项数据，使用默认数据");
            }
            // 只有在配置文件中确实没有数据时才使用默认数据
            const defaultPrizes = createDefaultPrizes('super_lotto');
            renderPrizeList('super-lotto-prizes', defaultPrizes);
        }
    }

    // 处理刮刮乐配置
    if (config.scratch_cards) {
        if (debugMode) {
            console.log("更新刮刮乐配置UI:", config.scratch_cards);
        }
        updateScratchCardsUI(config.scratch_cards);
    }

    // 处理刮刮乐符号配置
    if (config.scratch_symbols) {
        if (debugMode) {
            console.log("更新刮刮乐符号配置UI:", config.scratch_symbols);
        }
        updateScratchSymbolsUI(config.scratch_symbols);
    }

    // 不自动切换标签页，保持用户当前所在的标签页
}

// 更新彩票配置UI（会切换到双色球标签页，用于初始加载）
function updateLotteryConfigUI(config) {
    if (!config) return;

    if (debugMode) {
        console.log("正在更新彩票配置UI:", config);
    }

    // 存储配置数据到全局变量，供其他函数使用
    window.lotteryConfigData = config;

    // 刷新所有已显示的物品名称
    refreshAllItemDisplayNames();
    
    // 处理双色球配置
    if (config.double_ball) {
        if (debugMode) {
            console.log("更新双色球配置:", config.double_ball);
        }

        const priceInput = document.getElementById('double-ball-price');
        const probInput = document.getElementById('double-ball-prob');

        if (priceInput) {
            priceInput.value = config.double_ball.price || '';
            if (debugMode) {
                console.log("设置双色球价格:", config.double_ball.price);
            }
        } else {
            if (debugMode) {
                console.error("找不到双色球价格输入框元素");
            }
        }

        if (probInput) {
            probInput.value = config.double_ball.probability || '';
            if (debugMode) {
                console.log("设置双色球概率:", config.double_ball.probability);
            }
        } else {
            if (debugMode) {
                console.error("找不到双色球概率输入框元素");
            }
        }

        // 更新开奖设置
        if (config.double_ball.drawTime) {
            const hourInput = document.getElementById('double-ball-draw-hour');
            const minuteInput = document.getElementById('double-ball-draw-minute');

            if (hourInput) {
                hourInput.value = config.double_ball.drawTime.hour || 0;
            }
            if (minuteInput) {
                minuteInput.value = config.double_ball.drawTime.minute || 0;
            }
        }
        
        // 更新开奖日期复选框
        if (config.double_ball.drawDays && Array.isArray(config.double_ball.drawDays)) {
            // 先清空所有复选框
            for (let i = 1; i <= 7; i++) {
                const checkbox = document.getElementById(`double-ball-day-${i}`);
                if (checkbox) checkbox.checked = false;
            }
            
            // 然后根据配置选中对应的复选框
            config.double_ball.drawDays.forEach(day => {
                const checkbox = document.getElementById(`double-ball-day-${day}`);
                if (checkbox) checkbox.checked = true;
            });
        }
        
        // 渲染双色球奖项 - 直接使用配置文件中的数据
        if (config.double_ball.prizes && Object.keys(config.double_ball.prizes).length > 0) {
            if (debugMode) {
                console.log("使用配置文件中的双色球奖项数据");
            }
            renderPrizeList('double-ball-prizes', config.double_ball.prizes);
        } else {
            if (debugMode) {
                console.log("配置文件中没有双色球奖项数据，使用默认数据");
            }
            // 只有在配置文件中确实没有数据时才使用默认数据
            const defaultPrizes = createDefaultPrizes('double_ball');
            renderPrizeList('double-ball-prizes', defaultPrizes);
        }
    }
    
    // 处理刮刮乐配置 - 使用专门的更新函数
    if (config.scratch_cards) {
        if (debugMode) {
            console.log("更新刮刮乐配置UI:", config.scratch_cards);
        }
        updateScratchCardsUI(config.scratch_cards);
    }

    // 处理刮刮乐符号配置 - 使用专门的更新函数
    if (config.scratch_symbols) {
        if (debugMode) {
            console.log("更新刮刮乐符号配置UI:", config.scratch_symbols);
        }
        updateScratchSymbolsUI(config.scratch_symbols);
    }
    
    // 处理大乐透配置
    if (config.super_lotto) {
        if (debugMode) {
            console.log("更新大乐透配置:", config.super_lotto);
        }

        const priceInput = document.getElementById('super-lotto-price');
        const probInput = document.getElementById('super-lotto-prob');

        if (priceInput) {
            priceInput.value = config.super_lotto.price || '';
            if (debugMode) {
                console.log("设置大乐透价格:", config.super_lotto.price);
            }
        } else {
            if (debugMode) {
                console.error("找不到大乐透价格输入框元素");
            }
        }

        if (probInput) {
            probInput.value = config.super_lotto.probability || '';
            if (debugMode) {
                console.log("设置大乐透概率:", config.super_lotto.probability);
            }
        } else {
            if (debugMode) {
                console.error("找不到大乐透概率输入框元素");
            }
        }
        
        // 更新开奖设置
        if (config.super_lotto.drawTime) {
            document.getElementById('super-lotto-draw-hour').value = config.super_lotto.drawTime.hour || 0;
            document.getElementById('super-lotto-draw-minute').value = config.super_lotto.drawTime.minute || 0;
        }
        
        // 更新开奖日期复选框
        if (config.super_lotto.drawDays && Array.isArray(config.super_lotto.drawDays)) {
            // 先清空所有复选框
            for (let i = 1; i <= 7; i++) {
                const checkbox = document.getElementById(`super-lotto-day-${i}`);
                if (checkbox) checkbox.checked = false;
            }
            
            // 然后根据配置选中对应的复选框
            config.super_lotto.drawDays.forEach(day => {
                const checkbox = document.getElementById(`super-lotto-day-${day}`);
                if (checkbox) checkbox.checked = true;
            });
        }
        
        // 渲染大乐透奖项 - 直接使用配置文件中的数据
        if (config.super_lotto.prizes && Object.keys(config.super_lotto.prizes).length > 0) {
            if (debugMode) {
                console.log("使用配置文件中的大乐透奖项数据");
            }
            renderPrizeList('super-lotto-prizes', config.super_lotto.prizes);
        } else {
            if (debugMode) {
                console.log("配置文件中没有大乐透奖项数据，使用默认数据");
            }
            // 只有在配置文件中确实没有数据时才使用默认数据
            const defaultPrizes = createDefaultPrizes('super_lotto');
            renderPrizeList('super-lotto-prizes', defaultPrizes);
        }
    }

    // 处理排列5配置
    if (config.arrange_five) {
        if (debugMode) {
            console.log("更新排列5配置:", config.arrange_five);
        }

        const priceInput = document.getElementById('arrange-five-price');
        const probInput = document.getElementById('arrange-five-prob');

        if (priceInput) {
            priceInput.value = config.arrange_five.price || '';
            if (debugMode) {
                console.log("设置排列5价格:", config.arrange_five.price);
            }
        } else {
            if (debugMode) {
                console.error("找不到排列5价格输入框元素");
            }
        }

        if (probInput) {
            // 排列5概率固定为0.001%
            probInput.value = '0.001';
            if (debugMode) {
                console.log("设置排列5概率: 0.001%");
            }
        } else {
            if (debugMode) {
                console.error("找不到排列5概率输入框元素");
            }
        }

        // 渲染排列5物品配置 - 使用与彩钻相同的渲染方式
        if (config.arrange_five.prizes && config.arrange_five.prizes['1'] && config.arrange_five.prizes['1'].itemRewards && Object.keys(config.arrange_five.prizes['1'].itemRewards).length > 0) {
            if (debugMode) {
                console.log("使用配置文件中的排列5物品数据");
            }
            renderScratchRates('arrange-five-items', config.arrange_five.prizes['1'].itemRewards);
        } else {
            if (debugMode) {
                console.log("配置文件中没有排列5物品数据，使用默认数据");
            }
            // 创建默认物品配置
            const defaultItems = {
                'bread': 25,
                'water': 20,
                'bandage': 18,
                'phone': 12,
                'lockpick': 10,
                'diamond': 8,
                'gold': 5,
                'weapon_pistol': 2,
                'money:5000': 1,
                'car_key': 0.5
            };
            renderScratchRates('arrange-five-items', defaultItems);
        }
    }

    // 处理刮刮乐配置
    if (config.scratch_cards) {
        if (debugMode) {
            console.log("更新刮刮乐配置UI:", config.scratch_cards);
        }
        updateScratchCardsUI(config.scratch_cards);
    }

    // 处理刮刮乐符号配置
    if (config.scratch_symbols) {
        if (debugMode) {
            console.log("更新刮刮乐符号配置UI:", config.scratch_symbols);
        }
        updateScratchSymbolsUI(config.scratch_symbols);
    }

    // 处理刮刮乐配置
    if (config.scratch_cards) {
        if (debugMode) {
            console.log("更新刮刮乐配置UI:", config.scratch_cards);
        }
        updateScratchCardsUI(config.scratch_cards);
    }

    // 处理刮刮乐符号配置
    if (config.scratch_symbols) {
        if (debugMode) {
            console.log("更新刮刮乐符号配置UI:", config.scratch_symbols);
        }
        updateScratchSymbolsUI(config.scratch_symbols);
    }

    // 不自动切换标签页，保持用户当前所在的标签页
    // switchLotteryTab('double-ball');
}

// 更新刮刮乐配置UI
function updateScratchCardsUI(scratchCards) {
    // 更新喜相逢配置
    if (scratchCards.scratch_xixiangfeng) {
        const config = scratchCards.scratch_xixiangfeng;

        // 更新基本配置
        if (config.price !== undefined) {
            const priceInput = document.getElementById('scratch-xixiangfeng-price');
            if (priceInput) priceInput.value = config.price;
        }

        if (config.maxPrize !== undefined) {
            const maxPrizeInput = document.getElementById('scratch-xixiangfeng-maxprize');
            if (maxPrizeInput) maxPrizeInput.value = config.maxPrize;
        }

        // 更新金额权重配置
        if (config.amountRates) {
            if (debugMode) {
                console.log("更新喜相逢金额权重配置:", config.amountRates);
            }
            renderScratchRates('scratch-xixiangfeng-amount-rates', config.amountRates);
        }
    }

    // 更新福鼠送彩配置
    if (scratchCards.scratch_fusong) {
        const config = scratchCards.scratch_fusong;

        if (config.price !== undefined) {
            const priceInput = document.getElementById('scratch-fusong-price');
            if (priceInput) priceInput.value = config.price;
        }

        if (config.maxPrize !== undefined) {
            const maxPrizeInput = document.getElementById('scratch-fusong-maxprize');
            if (maxPrizeInput) maxPrizeInput.value = config.maxPrize;
        }

        // 更新行金额权重配置
        if (config.rowAmountRates) {
            if (debugMode) {
                console.log("更新福鼠送彩行金额权重配置:", config.rowAmountRates);
            }
            renderScratchRates('scratch-fusong-row-amount-rates', config.rowAmountRates);
        }
    }

    // 更新耀出彩配置
    if (scratchCards.scratch_yaocai) {
        const config = scratchCards.scratch_yaocai;

        if (config.price !== undefined) {
            const priceInput = document.getElementById('scratch-yaocai-price');
            if (priceInput) priceInput.value = config.price;
        }

        if (config.maxPrize !== undefined) {
            const maxPrizeInput = document.getElementById('scratch-yaocai-maxprize');
            if (maxPrizeInput) maxPrizeInput.value = config.maxPrize;
        }

        // 更新中奖金额权重配置
        if (config.winningAmountRates) {
            if (debugMode) {
                console.log("更新耀出彩中奖金额权重配置:", config.winningAmountRates);
            }
            renderScratchRates('scratch-yaocai-winning-amount-rates', config.winningAmountRates);
        }
    }

    // 更新5倍彩钻配置
    if (scratchCards.scratch_caizuan) {
        const config = scratchCards.scratch_caizuan;

        if (config.price !== undefined) {
            const priceInput = document.getElementById('scratch-caizuan-price');
            if (priceInput) priceInput.value = config.price;
        }

        if (config.maxPrize !== undefined) {
            const maxPrizeInput = document.getElementById('scratch-caizuan-maxprize');
            if (maxPrizeInput) maxPrizeInput.value = config.maxPrize;
        }

        // 更新中奖物品权重配置
        if (config.winningItemRates) {
            if (debugMode) {
                console.log("更新5倍彩钻中奖物品权重配置:", config.winningItemRates);
            }
            renderScratchRates('scratch-caizuan-winning-item-rates', config.winningItemRates);
        }
    }

    // 更新中国福配置
    if (scratchCards.scratch_zhongguofu) {
        const config = scratchCards.scratch_zhongguofu;

        if (config.price !== undefined) {
            const priceInput = document.getElementById('scratch-zhongguofu-price');
            if (priceInput) priceInput.value = config.price;
        }

        if (config.maxPrize !== undefined) {
            const maxPrizeInput = document.getElementById('scratch-zhongguofu-maxprize');
            if (maxPrizeInput) maxPrizeInput.value = config.maxPrize;
        }

        // 更新中奖物品权重配置
        if (config.winningItemRates) {
            if (debugMode) {
                console.log("更新中国福中奖物品权重配置:", config.winningItemRates);
            }
            renderScratchRates('scratch-zhongguofu-winning-item-rates', config.winningItemRates);
        }
    }

    // 更新乘风破浪配置
    if (scratchCards.scratch_chengfeng) {
        const config = scratchCards.scratch_chengfeng;

        if (config.price !== undefined) {
            const priceInput = document.getElementById('scratch-chengfeng-price');
            if (priceInput) priceInput.value = config.price;
        }

        if (config.maxPrize !== undefined) {
            const maxPrizeInput = document.getElementById('scratch-chengfeng-maxprize');
            if (maxPrizeInput) maxPrizeInput.value = config.maxPrize;
        }

        // 更新中奖符号物品权重配置
        if (config.winningItemRates) {
            if (debugMode) {
                console.log("更新乘风破浪中奖符号物品权重配置:", config.winningItemRates);
            }
            renderScratchRates('scratch-chengfeng-winning-item-rates', config.winningItemRates);
        }

        // 更新图标物品权重配置
        if (config.iconItemRates) {
            if (debugMode) {
                console.log("更新乘风破浪图标物品权重配置:", config.iconItemRates);
            }
            renderScratchRates('scratch-chengfeng-icon-item-rates', config.iconItemRates);
        }
    }
}

// 更新刮刮乐符号配置UI
function updateScratchSymbolsUI(scratchSymbols) {
    // 更新喜相逢符号配置
    if (scratchSymbols.xiRate !== undefined) {
        const xiRateInput = document.getElementById('scratch-xixiangfeng-xirate');
        if (xiRateInput) xiRateInput.value = scratchSymbols.xiRate;
    }

    if (scratchSymbols.xiXiRate !== undefined) {
        const xiXiRateInput = document.getElementById('scratch-xixiangfeng-xixirate');
        if (xiXiRateInput) xiXiRateInput.value = scratchSymbols.xiXiRate;
    }

    if (scratchSymbols.allSpecialRate !== undefined) {
        const allSpecialRateInput = document.getElementById('scratch-xixiangfeng-allspecialrate');
        if (allSpecialRateInput) allSpecialRateInput.value = scratchSymbols.allSpecialRate;
    }

    // 更新福鼠送彩匹配数量权重
    if (scratchSymbols.fusongMatchRates) {
        if (debugMode) {
            console.log("更新福鼠送彩匹配数量权重配置:", scratchSymbols.fusongMatchRates);
        }
        renderScratchRates('scratch-fusong-match-rates', scratchSymbols.fusongMatchRates);
    }

    // 更新耀出彩匹配数量权重
    if (scratchSymbols.yaocaiMatchRates) {
        if (debugMode) {
            console.log("更新耀出彩匹配数量权重配置:", scratchSymbols.yaocaiMatchRates);
        }
        renderScratchRates('scratch-yaocai-match-rates', scratchSymbols.yaocaiMatchRates);
    }

    // 更新5倍彩钻匹配数量权重
    if (scratchSymbols.caizuanMatchRates) {
        if (debugMode) {
            console.log("更新5倍彩钻匹配数量权重配置:", scratchSymbols.caizuanMatchRates);
        }
        renderScratchRates('scratch-caizuan-match-rates', scratchSymbols.caizuanMatchRates);
    }

    // 更新5倍彩钻钻石图案数量权重
    if (scratchSymbols.caizuanDiamondRates) {
        if (debugMode) {
            console.log("更新5倍彩钻钻石图案数量权重配置:", scratchSymbols.caizuanDiamondRates);
        }
        renderScratchRates('scratch-caizuan-diamond-rates', scratchSymbols.caizuanDiamondRates);
    }

    // 更新中国福匹配数量权重
    if (scratchSymbols.zhongguofuMatchRates) {
        if (debugMode) {
            console.log("更新中国福匹配数量权重配置:", scratchSymbols.zhongguofuMatchRates);
        }
        renderScratchRates('scratch-zhongguofu-match-rates', scratchSymbols.zhongguofuMatchRates);
    }

    // 更新中国福福符号数量权重
    if (scratchSymbols.zhongguofuFuRates) {
        if (debugMode) {
            console.log("更新中国福福符号数量权重配置:", scratchSymbols.zhongguofuFuRates);
        }
        renderScratchRates('scratch-zhongguofu-fu-rates', scratchSymbols.zhongguofuFuRates);
    }

    // 更新乘风破浪⛵符号数量权重
    if (scratchSymbols.chengfengSailRates) {
        if (debugMode) {
            console.log("更新乘风破浪⛵符号数量权重配置:", scratchSymbols.chengfengSailRates);
        }
        renderScratchRates('scratch-chengfeng-sail-rates', scratchSymbols.chengfengSailRates);
    }

    // 更新乘风破浪🌊符号数量权重
    if (scratchSymbols.chengfengTornadoRates) {
        if (debugMode) {
            console.log("更新乘风破浪🌊符号数量权重配置:", scratchSymbols.chengfengTornadoRates);
        }
        renderScratchRates('scratch-chengfeng-tornado-rates', scratchSymbols.chengfengTornadoRates);
    }
}

// 渲染刮刮乐权重配置
function renderScratchRates(containerId, rates) {
    if (debugMode) {
        console.log("开始渲染刮刮乐权重配置 - 容器ID:", containerId, "权重数据:", rates);
    }

    const container = document.getElementById(containerId);
    if (!container) {
        if (debugMode) {
            console.error("找不到权重配置容器:", containerId);
        }
        return;
    }

    if (debugMode) {
        console.log("找到权重配置容器:", containerId);
    }

    // 清空容器
    container.innerHTML = '';

    if (!rates || Object.keys(rates).length === 0) {
        if (debugMode) {
            console.log("权重数据为空，显示空消息");
        }
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'empty-rates-message';
        emptyMessage.innerHTML = `
            <div style="font-size: 48px; margin-bottom: 10px;">📊</div>
            <div>没有可用的权重配置</div>
            <div style="font-size: 14px; margin-top: 8px; color: #adb5bd;">请联系管理员配置权重数据</div>
        `;
        container.appendChild(emptyMessage);
        return;
    }

    if (debugMode) {
        console.log("权重数据有效，开始渲染", Object.keys(rates).length, "个权重项");
    }

    // 获取所有金额/数量，并按数字大小排序
    const sortedKeys = Object.keys(rates).sort((a, b) => {
        return parseFloat(a) - parseFloat(b);
    });

    // 计算统计信息
    let totalWeight = 0;
    let validItems = 0;

    // 创建每个权重的DOM
    sortedKeys.forEach((key, index) => {
        const value = parseFloat(rates[key]) || 0;
        totalWeight += value;
        if (value > 0) validItems++;

        const rateItem = document.createElement('div');
        rateItem.className = 'rate-item';
        rateItem.style.animationDelay = `${index * 0.1}s`;

        // 金额/数量标签
        const rateKey = document.createElement('div');
        rateKey.className = 'rate-key';

        // 根据容器类型显示不同的标签格式
        if (containerId.includes('amount')) {
            rateKey.textContent = `${key}元`;
        } else if (containerId.includes('match')) {
            rateKey.textContent = `${key}个匹配`;
        } else if (containerId.includes('diamond')) {
            rateKey.textContent = `${key}个钻石`;
        } else if (containerId.includes('winning-item') || containerId.includes('icon-item') || containerId.includes('arrange-five-items')) {
            // 物品权重配置 - 可编辑的物品选择器
            rateKey.className = 'rate-key item-selector';

            // 创建物品选择器容器
            const itemSelectorContainer = document.createElement('div');
            itemSelectorContainer.className = 'item-selector-container';

            // 创建显示当前物品的按钮
            const itemDisplayButton = document.createElement('button');
            itemDisplayButton.className = 'item-display-button';
            itemDisplayButton.type = 'button';

            const displayName = getItemDisplayName(key);
            itemDisplayButton.innerHTML = `
                <span class="item-name">${displayName}</span>
                <i class="fas fa-chevron-down"></i>
            `;
            itemDisplayButton.title = `物品代码: ${key}`;

            // 创建下拉选择框
            const itemDropdown = document.createElement('div');
            itemDropdown.className = 'item-dropdown';
            itemDropdown.style.display = 'none';

            // 添加所有可用物品选项
            const allItems = getAllAvailableItems();
            allItems.forEach(item => {
                const option = document.createElement('div');
                option.className = 'item-option';
                option.dataset.itemCode = item.code;

                if (item.code === 'money') {
                    // 金钱物品特殊处理 - 显示数量输入
                    const currentAmount = key.includes('money:') ? key.split(':')[1] : '1000';
                    option.innerHTML = `
                        <div class="item-info">
                            <span class="item-name">${item.name}</span>
                            <input type="number" class="money-amount-input" value="${currentAmount}"
                                   min="1" max="1000000" placeholder="输入金额" onclick="event.stopPropagation()">
                        </div>
                    `;
                } else {
                    option.innerHTML = `
                        <div class="item-info">
                            <span class="item-name">${item.name}</span>
                        </div>
                    `;
                }

                // 标记当前选中的物品
                if ((item.code === 'money' && key.includes('money:')) || item.code === key) {
                    option.classList.add('selected');
                }

                // 添加点击事件 - 注意：input变量将在后面定义
                option.addEventListener('click', function(e) {
                    e.stopPropagation();
                    // 获取对应的权重输入框
                    const weightInput = rateItem.querySelector('.rate-value-input input');
                    selectItemForRate(key, item, itemDisplayButton, itemDropdown, weightInput);
                });

                itemDropdown.appendChild(option);
            });

            // 添加按钮点击事件
            itemDisplayButton.addEventListener('click', function(e) {
                e.stopPropagation();
                toggleItemDropdown(itemDropdown);
            });

            // 组装物品选择器
            itemSelectorContainer.appendChild(itemDisplayButton);
            itemSelectorContainer.appendChild(itemDropdown);
            rateKey.appendChild(itemSelectorContainer);

            // 点击其他地方关闭下拉框
            document.addEventListener('click', function(e) {
                // 如果点击的是输入框或下拉框内部，不关闭下拉框
                if (e.target.closest('.item-dropdown') ||
                    e.target.closest('.item-display-button') ||
                    e.target.tagName === 'INPUT') {
                    return;
                }

                itemDropdown.style.display = 'none';
                const rateItem = itemDropdown.closest('.rate-item');
                if (rateItem) {
                    rateItem.classList.remove('dropdown-open');
                }
            });
        } else {
            rateKey.textContent = key;
        }

        // 权重输入
        const rateValueInput = document.createElement('div');
        rateValueInput.className = 'rate-value-input';

        const input = document.createElement('input');
        input.type = 'number';
        input.min = '0.001';
        input.step = '0.001';
        input.value = value || 0;
        input.placeholder = '输入权重值 (0.001-1.0)';
        input.dataset.key = key;
        input.title = `设置 ${key} 的权重值，数值越大出现概率越高`;

        // 添加输入验证
        input.addEventListener('input', function() {
            const val = parseFloat(this.value);
            if (val < 0.001 || val > 1) {
                this.style.borderColor = '#dc3545';
                this.style.backgroundColor = '#f8d7da';
            } else {
                this.style.borderColor = '#28a745';
                this.style.backgroundColor = '#d4edda';
            }

            // 实时更新统计信息
            updateRatesSummary(containerId);
        });

        rateValueInput.appendChild(input);

        // 为物品权重配置添加删除按钮
        if (containerId.includes('winning-item') || containerId.includes('icon-item') || containerId.includes('arrange-five-items')) {
            const deleteButton = document.createElement('button');
            deleteButton.className = 'btn-delete-item';
            deleteButton.type = 'button';
            deleteButton.innerHTML = '<i class="fas fa-trash"></i>';
            deleteButton.title = '删除此物品';
            deleteButton.addEventListener('click', function() {
                rateItem.remove();
                updateRatesSummary(containerId);
                markConfigAsModified();
                showNotification('物品已删除，请点击保存配置按钮保存更改', 'info');
            });
            rateValueInput.appendChild(deleteButton);
        }

        // 添加到容器
        rateItem.appendChild(rateKey);
        rateItem.appendChild(rateValueInput);
        container.appendChild(rateItem);
    });

    // 为物品权重配置添加"添加新物品"按钮
    if (containerId.includes('winning-item') || containerId.includes('icon-item') || containerId.includes('arrange-five-items')) {
        addNewItemButton(container, containerId);
    }

    // 添加统计信息
    addRatesSummary(container, containerId, sortedKeys.length, validItems, totalWeight);
}

// 添加权重配置统计信息
function addRatesSummary(container, containerId, totalItems, validItems, totalWeight) {
    const summary = document.createElement('div');
    summary.className = 'rates-summary';
    summary.innerHTML = `
        <div class="summary-item">
            <div class="summary-label">总项目</div>
            <div class="summary-value">${totalItems}</div>
        </div>
        <div class="summary-item">
            <div class="summary-label">已配置</div>
            <div class="summary-value">${validItems}</div>
        </div>
        <div class="summary-item">
            <div class="summary-label">总权重</div>
            <div class="summary-value">${totalWeight.toFixed(3)}</div>
        </div>
        <div class="summary-item">
            <div class="summary-label">平均权重</div>
            <div class="summary-value">${validItems > 0 ? (totalWeight / validItems).toFixed(3) : '0.000'}</div>
        </div>
    `;
    container.appendChild(summary);
}

// 更新权重配置统计信息
function updateRatesSummary(containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;

    const inputs = container.querySelectorAll('.rate-value-input input');
    let totalWeight = 0;
    let validItems = 0;

    inputs.forEach(input => {
        const value = parseFloat(input.value) || 0;
        if (value > 0) {
            totalWeight += value;
            validItems++;
        }
    });

    const summary = container.querySelector('.rates-summary');
    if (summary) {
        const summaryValues = summary.querySelectorAll('.summary-value');
        if (summaryValues.length >= 4) {
            summaryValues[1].textContent = validItems;
            summaryValues[2].textContent = totalWeight.toFixed(3);
            summaryValues[3].textContent = validItems > 0 ? (totalWeight / validItems).toFixed(3) : '0.000';
        }
    }
}

// 渲染奖项列表
function renderPrizeList(containerId, prizes) {
    if (debugMode) {
        console.log(`开始渲染奖项列表到容器: ${containerId}`, prizes);
    }

    const container = document.getElementById(containerId);
    if (!container) {
        if (debugMode) {
            console.error(`找不到容器元素: ${containerId}`);
        }
        return;
    }
    
    // 清空容器
    container.innerHTML = '';
    
    // 将对象转换为数组并排序
    const sortedPrizes = [];
    for (const id in prizes) {
        if (prizes.hasOwnProperty(id)) {
            sortedPrizes.push({
                id: id,
                ...prizes[id]
            });
        }
    }
    
    // 按ID排序
    sortedPrizes.sort((a, b) => parseInt(a.id) - parseInt(b.id));
    
    if (debugMode) {
        console.log(`排序后的奖项数量: ${sortedPrizes.length}`);
    }
    
    // 如果没有奖项，添加提示
    if (sortedPrizes.length === 0) {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'empty-prize-message';
        emptyMessage.textContent = '没有可用的奖项配置';
        container.appendChild(emptyMessage);
        return;
    }
    
    // 创建每个奖项的DOM
    sortedPrizes.forEach(prize => {
        try {
            const prizeItem = document.createElement('div');
            prizeItem.className = 'prize-item';
            prizeItem.dataset.prizeId = prize.id;
            
            // 奖项名称
            const prizeName = document.createElement('div');
            prizeName.className = 'prize-name';
            prizeName.textContent = prize.name || `奖项 ${prize.id}`;
            
            // 中奖条件
            const prizeMatch = document.createElement('div');
            prizeMatch.className = 'prize-match';
            
            if (prize.match) {
                // 双色球或大乐透格式显示
                if (containerId === 'double-ball-prizes') {
                    prizeMatch.textContent = `红球: ${prize.match[0]}, 蓝球: ${prize.match[1]}`;
                } else {
                    prizeMatch.textContent = `前区: ${prize.match[0]}, 后区: ${prize.match[1]}`;
                }
            }
            
            // 奖金金额输入
            const prizeAmountInput = document.createElement('div');
            prizeAmountInput.className = 'prize-amount-input';
            
            const input = document.createElement('input');
            input.type = 'number';
            input.min = '1';
            input.value = prize.amount || 0;
            input.placeholder = '奖金金额';
            input.dataset.prizeId = prize.id;
            
            const currencySpan = document.createElement('span');
            currencySpan.textContent = '¥';
            
            prizeAmountInput.appendChild(input);
            prizeAmountInput.appendChild(currencySpan);
            
            // 组合所有元素
            prizeItem.appendChild(prizeName);
            prizeItem.appendChild(prizeMatch);
            prizeItem.appendChild(prizeAmountInput);
            
            container.appendChild(prizeItem);
        } catch (error) {
            console.error(`渲染奖项${prize.id}失败:`, error);
        }
    });
    
    if (debugMode) {
        console.log(`奖项列表渲染完成，总共渲染了 ${sortedPrizes.length} 个奖项`);
    }
}

// 获取双色球奖项配置
function getDoubleBallPrizeConfig() {
    const prizeContainer = document.getElementById('double-ball-prizes');
    if (!prizeContainer) return {};
    
    const prizeInputs = prizeContainer.querySelectorAll('input[data-prize-id]');
    const prizes = {};
    
    prizeInputs.forEach(input => {
        const prizeId = input.dataset.prizeId;
        const amount = parseInt(input.value);
        
        if (!isNaN(amount) && amount > 0) {
            prizes[prizeId] = amount;
        }
    });
    
    return prizes;
}

// 获取大乐透奖项配置
function getSuperLottoPrizeConfig() {
    const prizeContainer = document.getElementById('super-lotto-prizes');
    if (!prizeContainer) return {};
    
    const prizeInputs = prizeContainer.querySelectorAll('input[data-prize-id]');
    const prizes = {};
    
    prizeInputs.forEach(input => {
        const prizeId = input.dataset.prizeId;
        const amount = parseInt(input.value);
        
        if (!isNaN(amount) && amount > 0) {
            prizes[prizeId] = amount;
        }
    });
    
    return prizes;
}

// 保存双色球奖项配置
function saveDoubleBallPrizes() {
    console.log("🔥🔥🔥 双色球奖项保存按钮被点击！🔥🔥🔥");
    console.log("开始保存双色球奖项配置");
    const prizes = {};
    
    // 获取所有双色球奖项输入
    const inputs = document.querySelectorAll('#double-ball-prizes .prize-item input[data-prize-id]');
    console.log(`找到 ${inputs.length} 个双色球奖项输入框`);
    
    // 收集所有奖项金额
    inputs.forEach((input, index) => {
        const prizeId = input.dataset.prizeId;
        const amount = parseInt(input.value);

        console.log(`输入框 ${index}: prizeId=${prizeId}, value="${input.value}", amount=${amount}`);

        if (!isNaN(amount) && amount >= 0) {
            prizes[prizeId] = amount;
            console.log(`✅ 奖项 ${prizeId} 金额设置为 ${amount}`);
        } else {
            console.log(`❌ 奖项 ${prizeId} 金额无效: "${input.value}"`);
        }
    });
    
    // 检查是否有有效数据
    if (Object.keys(prizes).length === 0) {
        showNotification('没有有效的奖项配置，请检查输入', 'error');
        return;
    }
    
    console.log("发送双色球奖项配置数据:", prizes);
    
    // 发送配置到服务器
    fetch(`https://${GetParentResourceName()}/savePrizeConfig`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            type: 'double_ball',
            prizes: prizes
        })
    });
    
    showNotification('正在保存双色球奖项配置...', 'info');
}

// 保存大乐透奖项配置
function saveSuperLottoPrizes() {
    console.log("🔥🔥🔥 大乐透奖项保存按钮被点击！🔥🔥🔥");
    console.log("开始保存大乐透奖项配置");
    const prizes = {};
    
    // 获取所有大乐透奖项输入
    const inputs = document.querySelectorAll('#super-lotto-prizes .prize-item input[data-prize-id]');
    console.log(`找到 ${inputs.length} 个大乐透奖项输入框`);
    
    // 收集所有奖项金额
    inputs.forEach((input, index) => {
        const prizeId = input.dataset.prizeId;
        const amount = parseInt(input.value);

        console.log(`输入框 ${index}: prizeId=${prizeId}, value="${input.value}", amount=${amount}`);

        if (!isNaN(amount) && amount >= 0) {
            prizes[prizeId] = amount;
            console.log(`✅ 奖项 ${prizeId} 金额设置为 ${amount}`);
        } else {
            console.log(`❌ 奖项 ${prizeId} 金额无效: "${input.value}"`);
        }
    });
    
    // 检查是否有有效数据
    if (Object.keys(prizes).length === 0) {
        showNotification('没有有效的奖项配置，请检查输入', 'error');
        return;
    }
    
    console.log("发送大乐透奖项配置数据:", prizes);
    
    // 发送配置到服务器
    fetch(`https://${GetParentResourceName()}/savePrizeConfig`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            type: 'super_lotto',
            prizes: prizes
        })
    });
    
    showNotification('正在保存大乐透奖项配置...', 'info');
}

// 保存所有彩票配置
function saveAllLotteryConfig() {
    const doubleBallPrice = parseInt(document.getElementById('double-ball-price').value);
    const doubleBallProbability = parseFloat(document.getElementById('double-ball-prob').value);
    const superLottoPrice = parseInt(document.getElementById('super-lotto-price').value);
    const superLottoProbability = parseFloat(document.getElementById('super-lotto-prob').value);
    const arrangeFivePrice = parseInt(document.getElementById('arrange-five-price').value);
    const arrangeFiveProbability = 0.00001; // 排列5固定概率

    // 验证所有配置
    if (!validateLotteryConfig(doubleBallPrice, doubleBallProbability)) {
        showNotification('双色球配置验证失败', 'error');
        return;
    }

    if (!validateLotteryConfig(superLottoPrice, superLottoProbability)) {
        showNotification('大乐透配置验证失败', 'error');
        return;
    }

    // 验证排列5价格
    if (isNaN(arrangeFivePrice) || arrangeFivePrice < 0 || arrangeFivePrice > 10000000) {
        showNotification('排列5价格必须在0-10000000之间', 'error');
        return;
    }

    // 发送所有配置到服务器
    fetch(`https://${GetParentResourceName()}/saveAllLotteryConfig`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            double_ball: {
                price: doubleBallPrice,
                probability: doubleBallProbability
            },
            super_lotto: {
                price: superLottoPrice,
                probability: superLottoProbability
            },
            arrange_five: {
                price: arrangeFivePrice,
                probability: arrangeFiveProbability
            }
        })
    });

    showNotification('正在保存所有彩票配置...', 'info');
}

// 验证彩票配置是否有效
function validateLotteryConfig(price, probability) {
    // 验证价格
    if (isNaN(price) || price < 0 || price > 10000000) {
        showNotification('彩票价格必须在0-10000000之间', 'error');
        return false;
    }

    // 验证概率
    if (isNaN(probability) || probability < 0.001 || probability > 0.5) {
        showNotification('中奖概率必须在0.1%-50%之间', 'error');
        return false;
    }

    return true;
}

// 保存双色球配置
function saveDoubleBallConfig() {
    const price = parseInt(document.getElementById('double-ball-price').value);
    const probability = parseFloat(document.getElementById('double-ball-prob').value);

    if (!validateLotteryConfig(price, probability)) return;
    
    // 发送配置到服务器
    fetch(`https://${GetParentResourceName()}/saveLotteryConfig`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            type: 'double_ball',
            price: price,
            probability: probability
        })
    });

    showNotification('正在保存双色球配置...', 'info');
}

// 保存大乐透配置
function saveSuperLottoConfig() {
    const price = parseInt(document.getElementById('super-lotto-price').value);
    const probability = parseFloat(document.getElementById('super-lotto-prob').value);

    if (!validateLotteryConfig(price, probability)) return;
    
    // 发送配置到服务器
    fetch(`https://${GetParentResourceName()}/saveLotteryConfig`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            type: 'super_lotto',
            price: price,
            probability: probability
        })
    });

    showNotification('正在保存大乐透配置...', 'info');
}

// 保存排列5配置
function saveArrangeFiveConfig() {
    const price = parseInt(document.getElementById('arrange-five-price').value);
    const probability = 0.00001; // 排列5固定概率 1/100000

    // 验证价格
    if (isNaN(price) || price < 0 || price > 10000000) {
        showNotification('彩票价格必须在0-10000000之间', 'error');
        return;
    }

    // 发送配置到服务器
    fetch(`https://${GetParentResourceName()}/saveLotteryConfig`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            type: 'arrange_five',
            price: price,
            probability: probability
        })
    });

    showNotification('正在保存排列5配置...', 'info');
}

// 保存排列5物品配置 - 使用与彩钻相同的保存方式
function saveArrangeFiveItemRates() {
    // 使用与彩钻相同的保存逻辑
    saveScratchRates('arrange_five', 'itemRewards', 'arrange-five-items');
}

// 创建默认奖项配置
function createDefaultPrizes(type) {
    const prizes = {};
    
    if (type === 'double_ball') {
        // 双色球默认奖项
        prizes['1'] = { id: '1', name: '一等奖', match: [6, 1], amount: 5000000 };
        prizes['2'] = { id: '2', name: '二等奖', match: [6, 0], amount: 500000 };
        prizes['3'] = { id: '3', name: '三等奖', match: [5, 1], amount: 3000 };
        prizes['4'] = { id: '4', name: '四等奖', match: [5, 0], amount: 200 };
        prizes['5'] = { id: '5', name: '五等奖', match: [4, 1], amount: 200 };
        prizes['6'] = { id: '6', name: '六等奖', match: [4, 0], amount: 10 };
        prizes['7'] = { id: '7', name: '七等奖', match: [3, 1], amount: 10 };
        prizes['8'] = { id: '8', name: '八等奖', match: [2, 1], amount: 5 };
        prizes['9'] = { id: '9', name: '九等奖', match: [1, 1], amount: 5 };
        prizes['10'] = { id: '10', name: '十等奖', match: [0, 1], amount: 5 };
    } else if (type === 'super_lotto') {
        // 大乐透默认奖项
        prizes['1'] = { id: '1', name: '一等奖', match: [5, 2], amount: 5000000 };
        prizes['2'] = { id: '2', name: '二等奖', match: [5, 1], amount: 500000 };
        prizes['3'] = { id: '3', name: '三等奖', match: [5, 0], amount: 10000 };
        prizes['4'] = { id: '4', name: '四等奖', match: [4, 2], amount: 3000 };
        prizes['5'] = { id: '5', name: '五等奖', match: [4, 1], amount: 300 };
        prizes['6'] = { id: '6', name: '六等奖', match: [3, 2], amount: 300 };
        prizes['7'] = { id: '7', name: '七等奖', match: [4, 0], amount: 20 };
        prizes['8'] = { id: '8', name: '八等奖', match: [3, 1], amount: 20 };
        prizes['9'] = { id: '9', name: '九等奖', match: [2, 2], amount: 20 };
        prizes['10'] = { id: '10', name: '十等奖', match: [3, 0], amount: 5 };
        prizes['11'] = { id: '11', name: '十一等奖', match: [1, 2], amount: 5 };
        prizes['12'] = { id: '12', name: '十二等奖', match: [2, 1], amount: 5 };
        prizes['13'] = { id: '13', name: '十三等奖', match: [0, 2], amount: 5 };
    }
    
    return prizes;
}

// 创建刮刮乐默认权重配置
function createDefaultScratchRates(type) {
    const rates = {};
    
    if (type === 'scratch_xixiangfeng_amount') {
        // 喜相逢默认金额权重
        rates[5] = 30;
        rates[10] = 25;
        rates[20] = 15;
        rates[50] = 10;
        rates[100] = 8;
        rates[200] = 5;
        rates[500] = 3;
        rates[1000] = 2;
        rates[5000] = 1.5;
        rates[10000] = 0.5;
        rates[50000] = 0.1;
        rates[300000] = 0.01;
    } else if (type === 'scratch_fusong_row') {
        // 福鼠送彩默认行金额权重
        rates[10] = 30;
        rates[20] = 25;
        rates[50] = 20;
        rates[100] = 15;
        rates[200] = 8;
        rates[500] = 5;
        rates[1000] = 3;
        rates[2000] = 2;
        rates[5000] = 1;
        rates[10000] = 0.5;
        rates[50000] = 0.1;
        rates[250000] = 0.05;
    } else if (type === 'scratch_fusong_match') {
        // 福鼠送彩默认匹配数量权重
        rates[0] = 160;
        rates[1] = 100;
        rates[2] = 80;
        rates[3] = 20;
        rates[4] = 15;
        rates[5] = 10;
        rates[6] = 8;
        rates[7] = 6;
        rates[8] = 5;
        rates[9] = 0.5;
        rates[10] = 0.4;
        rates[15] = 0.3;
        rates[20] = 0.2;
        rates[25] = 0.1;
    } else if (type === 'scratch_yaocai_winning') {
        // 耀出彩默认中奖号码金额权重
        rates[50] = 30;
        rates[100] = 25;
        rates[200] = 20;
        rates[500] = 10;
        rates[1000] = 8;
        rates[2000] = 5;
        rates[5000] = 3;
        rates[10000] = 1;
        rates[50000] = 0.2;
        rates[200000] = 0.1;
        rates[500000] = 0.02;
        rates[800000] = 0.001;
    } else if (type === 'scratch_yaocai_match') {
        // 耀出彩默认匹配数量权重
        rates[0] = 160;
        rates[1] = 100;
        rates[2] = 25;
        rates[3] = 15;
        rates[4] = 10;
        rates[5] = 6;
        rates[6] = 4;
        rates[7] = 3;
        rates[8] = 2;
        rates[9] = 1;
        rates[10] = 0.5;
        rates[15] = 0.25;
        rates[20] = 0.1;
        rates[25] = 0.05;
    }
    
    return rates;
}
// 切换彩票配置标签页
function switchLotteryTab(tabName) {
    // 更新按钮激活状态
    const tabButtons = document.querySelectorAll('#lottery-config-tab .record-filter .filter-btn');
    tabButtons.forEach(btn => {
        btn.classList.remove('active');
        if(btn.dataset.lotteryTab === tabName) {
            btn.classList.add('active');
        }
    });
    
    // 更新标签页内容显示
    const tabs = document.querySelectorAll('.lottery-type-tab');
    tabs.forEach(tab => {
        tab.classList.remove('active');
    });
    
    const activeTab = document.getElementById(tabName + '-tab');
    if(activeTab) {
        activeTab.classList.add('active');
    }
}

// 验证刮刮乐配置是否有效
function validateScratchConfig(price, maxPrize) {
    // 验证价格
    if (isNaN(price) || price < 0 || price > 10000000) {
        showNotification('刮刮乐价格必须在0-10000000之间', 'error');
        return false;
    }

    // 验证最大奖金
    if (isNaN(maxPrize) || maxPrize < 1000 || maxPrize > 10000000) {
        showNotification('最大奖金必须在1000-10000000之间', 'error');
        return false;
    }

    return true;
}

// 保存喜相逢刮刮乐配置
function saveXixiangfengConfig() {
    const price = parseInt(document.getElementById('scratch-xixiangfeng-price').value);
    const maxPrize = parseInt(document.getElementById('scratch-xixiangfeng-maxprize').value);
    const xiRate = parseFloat(document.getElementById('scratch-xixiangfeng-xirate').value);
    const xiXiRate = parseFloat(document.getElementById('scratch-xixiangfeng-xixirate').value);
    const allSpecialRate = parseFloat(document.getElementById('scratch-xixiangfeng-allspecialrate').value);

    if (!validateScratchConfig(price, maxPrize)) return;
    
    // 验证概率值
    if (isNaN(xiRate) || xiRate < 0 || xiRate > 100) {
        showNotification('喜符号出现概率必须在0-100之间', 'error');
        return;
    }
    
    if (isNaN(xiXiRate) || xiXiRate < 0 || xiXiRate > 100) {
        showNotification('囍符号出现概率必须在0-100之间', 'error');
        return;
    }
    
    if (isNaN(allSpecialRate) || allSpecialRate < 0 || allSpecialRate > 100) {
        showNotification('全部是喜出现的概率必须在0-100之间', 'error');
        return;
    }
    
    // 使用刮刮乐权重配置函数保存基本配置
    saveScratchRates('scratch_xixiangfeng', 'config', 'scratch-xixiangfeng-config', {
        price: price,
        maxPrize: maxPrize,
        xiRate: xiRate,
        xiXiRate: xiXiRate,
        allSpecialRate: allSpecialRate
    });
}

// 保存福鼠送彩刮刮乐配置
function saveFusongConfig() {
    const price = parseInt(document.getElementById('scratch-fusong-price').value);
    const maxPrize = parseInt(document.getElementById('scratch-fusong-maxprize').value);

    if (!validateScratchConfig(price, maxPrize)) return;
    
    // 使用刮刮乐权重配置函数保存基本配置
    saveScratchRates('scratch_fusong', 'config', 'scratch-fusong-config', {
        price: price,
        maxPrize: maxPrize
    });
}

// 保存耀出彩刮刮乐配置
function saveYaocaiConfig() {
    const price = parseInt(document.getElementById('scratch-yaocai-price').value);
    const maxPrize = parseInt(document.getElementById('scratch-yaocai-maxprize').value);

    if (!validateScratchConfig(price, maxPrize)) return;

    // 使用刮刮乐权重配置函数保存基本配置
    saveScratchRates('scratch_yaocai', 'config', 'scratch-yaocai-config', {
        price: price,
        maxPrize: maxPrize
    });
}

// 保存5倍彩钻刮刮乐配置
function saveCaizuanConfig() {
    const price = parseInt(document.getElementById('scratch-caizuan-price').value);
    const maxPrize = parseInt(document.getElementById('scratch-caizuan-maxprize').value);

    if (!validateScratchConfig(price, maxPrize)) return;

    // 使用刮刮乐权重配置函数保存基本配置
    saveScratchRates('scratch_caizuan', 'config', 'scratch-caizuan-config', {
        price: price,
        maxPrize: maxPrize
    });
}

// 保存刮刮乐权重配置到服务器
function saveScratchRates(type, ratesType, containerId, directData = null) {
    let data = {};
    
    // 如果提供了直接数据，则使用它
    if (directData) {
        data = directData;
    } else {
        // 否则从容器中获取输入值
        const container = document.getElementById(containerId);
        if (!container) return;
        
        const inputs = container.querySelectorAll('input');
        const rates = {};
        
        // 收集所有输入的值
        inputs.forEach(input => {
            const key = input.dataset.key;
            const value = parseFloat(input.value);

            // 过滤掉无效的key和值（权重为0是有效的）
            if (key && key !== 'undefined' && key.trim() !== '' && !isNaN(value) && value >= 0) {
                rates[key] = value;
            }
        });
        
        // 检查是否有有效的权重配置
        if (Object.keys(rates).length === 0) {
            // 对于物品权重配置，允许保存空配置（删除所有项目）
            if (ratesType === 'winningItemRates') {
                showNotification('警告：保存空的物品配置，该刮刮乐将无法正常工作，请添加至少一个物品配置', 'warning');
                // 继续保存空配置，这样删除操作才能生效
                data = {};
            } else {
                showNotification('请至少设置一个有效的权重值', 'error');
                return;
            }
        } else {
            data = rates;
        }
    }
    
    // 发送配置到服务器
    fetch(`https://${GetParentResourceName()}/saveScratchRates`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            type: type,
            ratesType: ratesType,
            rates: data
        })
    });
    
    showNotification(`正在保存${type}配置...`, 'info');
}

// 保存喜相逢金额权重配置
function saveXixiangfengAmountRates() {
    saveScratchRates('scratch_xixiangfeng', 'amountRates', 'scratch-xixiangfeng-amount-rates');
}

// 保存福鼠送彩行金额权重配置
function saveFusongRowAmountRates() {
    saveScratchRates('scratch_fusong', 'rowAmountRates', 'scratch-fusong-row-amount-rates');
}

// 保存福鼠送彩匹配数量权重配置
function saveFusongMatchRates() {
    saveScratchRates('scratch_symbols', 'fusongMatchRates', 'scratch-fusong-match-rates');
}

// 保存耀出彩中奖号码金额权重配置
function saveYaocaiWinningAmountRates() {
    saveScratchRates('scratch_yaocai', 'winningAmountRates', 'scratch-yaocai-winning-amount-rates');
}

// 保存耀出彩匹配数量权重配置
function saveYaocaiMatchRates() {
    saveScratchRates('scratch_symbols', 'yaocaiMatchRates', 'scratch-yaocai-match-rates');
}

// 保存5倍彩钻中奖物品权重配置
function saveCaizuanWinningItemRates() {
    // 暂时禁用清理功能，直接保存
    // cleanupInvalidItemRates('scratch-caizuan-winning-item-rates');
    saveScratchRates('scratch_caizuan', 'winningItemRates', 'scratch-caizuan-winning-item-rates');
}

// 保存5倍彩钻匹配数量权重配置
function saveCaizuanMatchRates() {
    saveScratchRates('scratch_symbols', 'caizuanMatchRates', 'scratch-caizuan-match-rates');
}

// 保存5倍彩钻钻石图案数量权重配置
function saveCaizuanDiamondRates() {
    saveScratchRates('scratch_symbols', 'caizuanDiamondRates', 'scratch-caizuan-diamond-rates');
}

// 保存中国福基本配置
function saveZhongguofuConfig() {
    const price = parseInt(document.getElementById('scratch-zhongguofu-price').value);
    const maxPrize = parseInt(document.getElementById('scratch-zhongguofu-maxprize').value);

    if (!validateScratchConfig(price, maxPrize)) return;

    // 使用刮刮乐权重配置函数保存基本配置
    saveScratchRates('scratch_zhongguofu', 'config', 'scratch-zhongguofu-config', {
        price: price,
        maxPrize: maxPrize
    });
}

// 保存中国福中奖物品权重配置
function saveZhongguofuWinningItemRates() {
    saveScratchRates('scratch_zhongguofu', 'winningItemRates', 'scratch-zhongguofu-winning-item-rates');
}

// 保存中国福匹配数量权重配置
function saveZhongguofuMatchRates() {
    saveScratchRates('scratch_symbols', 'zhongguofuMatchRates', 'scratch-zhongguofu-match-rates');
}

// 保存中国福福符号数量权重配置
function saveZhongguofuFuRates() {
    saveScratchRates('scratch_symbols', 'zhongguofuFuRates', 'scratch-zhongguofu-fu-rates');
}

// 保存乘风破浪基本配置
function saveChengfengConfig() {
    const price = parseInt(document.getElementById('scratch-chengfeng-price').value);
    const maxPrize = parseInt(document.getElementById('scratch-chengfeng-maxprize').value);

    if (!validateScratchConfig(price, maxPrize)) return;

    // 使用刮刮乐权重配置函数保存基本配置
    saveScratchRates('scratch_chengfeng', 'config', 'scratch-chengfeng-config', {
        price: price,
        maxPrize: maxPrize
    });
}

// 保存乘风破浪中奖符号物品权重配置
function saveChengfengWinningItemRates() {
    saveScratchRates('scratch_chengfeng', 'winningItemRates', 'scratch-chengfeng-winning-item-rates');
}

// 保存乘风破浪图标物品权重配置
function saveChengfengIconItemRates() {
    saveScratchRates('scratch_chengfeng', 'iconItemRates', 'scratch-chengfeng-icon-item-rates');
}

// 保存乘风破浪⛵符号数量权重配置
function saveChengfengSailRates() {
    saveScratchRates('scratch_symbols', 'chengfengSailRates', 'scratch-chengfeng-sail-rates');
}

// 保存乘风破浪🌊符号数量权重配置
function saveChengfengTornadoRates() {
    saveScratchRates('scratch_symbols', 'chengfengTornadoRates', 'scratch-chengfeng-tornado-rates');
}

// 保存双色球开奖设置
function saveDoubleBallDrawSettings() {
    const drawHour = parseInt(document.getElementById('double-ball-draw-hour').value);
    const drawMinute = parseInt(document.getElementById('double-ball-draw-minute').value);
    
    // 验证时间
    if (isNaN(drawHour) || drawHour < 0 || drawHour > 23) {
        showNotification('开奖时间小时必须在0-23之间', 'error');
        return;
    }
    
    if (isNaN(drawMinute) || drawMinute < 0 || drawMinute > 59) {
        showNotification('开奖时间分钟必须在0-59之间', 'error');
        return;
    }
    
    // 获取选中的开奖日期
    const drawDays = [];
    for (let i = 1; i <= 7; i++) {
        const checkbox = document.getElementById(`double-ball-day-${i}`);
        if (checkbox && checkbox.checked) {
            drawDays.push(parseInt(checkbox.value));
        }
    }
    
    // 至少选择一天
    if (drawDays.length === 0) {
        showNotification('请至少选择一个开奖日期', 'error');
        return;
    }
    
    // 发送配置到服务器
    fetch(`https://${GetParentResourceName()}/saveDrawSettings`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            type: 'double_ball',
            drawTime: {
                hour: drawHour,
                minute: drawMinute
            },
            drawDays: drawDays
        })
    });
    
    showNotification('正在保存双色球开奖设置...', 'info');
}

// 保存大乐透开奖设置
function saveSuperLottoDrawSettings() {
    const drawHour = parseInt(document.getElementById('super-lotto-draw-hour').value);
    const drawMinute = parseInt(document.getElementById('super-lotto-draw-minute').value);
    
    // 验证时间
    if (isNaN(drawHour) || drawHour < 0 || drawHour > 23) {
        showNotification('开奖时间小时必须在0-23之间', 'error');
        return;
    }
    
    if (isNaN(drawMinute) || drawMinute < 0 || drawMinute > 59) {
        showNotification('开奖时间分钟必须在0-59之间', 'error');
        return;
    }
    
    // 获取选中的开奖日期
    const drawDays = [];
    for (let i = 1; i <= 7; i++) {
        const checkbox = document.getElementById(`super-lotto-day-${i}`);
        if (checkbox && checkbox.checked) {
            drawDays.push(parseInt(checkbox.value));
        }
    }
    
    // 至少选择一天
    if (drawDays.length === 0) {
        showNotification('请至少选择一个开奖日期', 'error');
        return;
    }
    
    // 发送配置到服务器
    fetch(`https://${GetParentResourceName()}/saveDrawSettings`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            type: 'super_lotto',
            drawTime: {
                hour: drawHour,
                minute: drawMinute
            },
            drawDays: drawDays
        })
    });

    showNotification('正在保存大乐透开奖设置...', 'info');
}

// 显示彩票配置页面
function showLotteryConfigPage() {
    // 显示彩票配置导航按钮
    const configNavBtn = document.getElementById('lottery-config-nav');
    if (configNavBtn) {
        configNavBtn.style.display = 'block';

        // 自动切换到彩票配置页面
        switchTab('lottery-config');

        // 加载彩票配置数据
        console.log("显示彩票配置页面，请求获取配置文件中的实际配置数据");
        getLotteryConfig();

        // 显示配置说明
        showConfigNotice();

        // 显示通知
        showNotification('彩票配置页面已显示', 'success');
    }
}